{"name": "@upstash/context7-mcp", "version": "1.0.0", "description": "MCP server for Context7", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc && chmod 755 dist/index.js", "format": "prettier --write .", "lint": "eslint \"**/*.{js,ts,tsx}\" --fix", "lint:check": "eslint \"**/*.{js,ts,tsx}\"", "start": "node dist/index.js --transport http"}, "repository": {"type": "git", "url": "git+https://github.com/upstash/context7.git"}, "keywords": ["modelcontextprotocol", "mcp", "context7"], "author": "ab<PERSON><PERSON>", "license": "MIT", "type": "module", "bin": {"context7-mcp": "dist/index.js"}, "files": ["dist"], "bugs": {"url": "https://github.com/upstash/context7/issues"}, "homepage": "https://github.com/upstash/context7#readme", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "commander": "^14.0.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^22.13.14", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.5", "prettier": "^3.5.3", "typescript": "^5.8.2", "typescript-eslint": "^8.28.0"}}
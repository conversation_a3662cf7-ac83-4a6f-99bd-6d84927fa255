# 核心事件总线系统
# 基于设计文档实现的高性能事件驱动架构

import time
import uuid
import threading
from typing import Dict, List, Any, Optional, Callable, Type
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum
import weakref

try:
    from evennia import DefaultScript, logger
    from evennia.utils import create

    # 处理Evennia延迟导入导致的None值
    if DefaultScript is None:
        DefaultScript = object
    if logger is None:
        logger = None
    if create is None:
        create = None

except ImportError:
    # 如果Evennia未正确配置，使用模拟类
    DefaultScript = object
    logger = None
    create = None


def safe_log(level: str, message: str):
    """安全的日志记录函数"""
    if logger:
        if level == "info":
            logger.log_info(message)
        elif level == "error":
            logger.log_err(message)
        elif level == "warn":
            logger.log_warn(message)
    else:
        print(f"[{level.upper()}] {message}")


class EventPriority(Enum):
    """事件优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class EventStatus(Enum):
    """事件状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BaseEvent:
    """基础事件类 - 所有事件的基类"""
    
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = ""
    timestamp: float = field(default_factory=time.time)
    source_id: Optional[str] = None
    target_id: Optional[str] = None
    data: Dict[str, Any] = field(default_factory=dict)
    priority: EventPriority = EventPriority.NORMAL
    status: EventStatus = EventStatus.PENDING
    retry_count: int = 0
    max_retries: int = 3
    
    # 性能统计
    processing_start_time: Optional[float] = None
    processing_end_time: Optional[float] = None
    
    def __post_init__(self):
        if not self.event_type:
            self.event_type = self.__class__.__name__
    
    def get_processing_time(self) -> Optional[float]:
        """获取处理时间"""
        if self.processing_start_time and self.processing_end_time:
            return self.processing_end_time - self.processing_start_time
        return None
    
    def mark_processing_start(self):
        """标记开始处理"""
        self.processing_start_time = time.time()
        self.status = EventStatus.PROCESSING
    
    def mark_processing_end(self, success: bool = True):
        """标记处理结束"""
        self.processing_end_time = time.time()
        self.status = EventStatus.COMPLETED if success else EventStatus.FAILED
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries
    
    def increment_retry(self):
        """增加重试次数"""
        self.retry_count += 1
        self.status = EventStatus.PENDING
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type,
            'timestamp': self.timestamp,
            'source_id': self.source_id,
            'target_id': self.target_id,
            'data': self.data,
            'priority': self.priority.value,
            'status': self.status.value,
            'retry_count': self.retry_count,
            'processing_time': self.get_processing_time()
        }


# 修仙系统事件类型
@dataclass
class CultivationBreakthroughEvent(BaseEvent):
    """修仙突破事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.HIGH


@dataclass
class CultivationProgressEvent(BaseEvent):
    """修仙进度事件"""
    pass


@dataclass
class CelestialAnomalyEvent(BaseEvent):
    """天象异常事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.CRITICAL


@dataclass
class SkillCastEvent(BaseEvent):
    """技能施放事件"""
    pass


@dataclass
class CombatStateEvent(BaseEvent):
    """战斗状态事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.HIGH


@dataclass
class SectConflictEvent(BaseEvent):
    """宗门冲突事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.HIGH


@dataclass
class MasterDiscipleEvent(BaseEvent):
    """师徒关系事件"""
    pass


@dataclass
class SpiritualEnergyEvent(BaseEvent):
    """灵气变化事件"""
    pass


@dataclass
class AIDirectorNotificationEvent(BaseEvent):
    """AI导演通知事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.HIGH


class EventFilter:
    """事件过滤器"""
    
    def __init__(self, 
                 event_types: Optional[List[str]] = None,
                 source_ids: Optional[List[str]] = None,
                 target_ids: Optional[List[str]] = None,
                 min_priority: Optional[EventPriority] = None,
                 custom_filter: Optional[Callable[[BaseEvent], bool]] = None):
        self.event_types = set(event_types) if event_types else None
        self.source_ids = set(source_ids) if source_ids else None
        self.target_ids = set(target_ids) if target_ids else None
        self.min_priority = min_priority
        self.custom_filter = custom_filter
    
    def matches(self, event: BaseEvent) -> bool:
        """检查事件是否匹配过滤条件"""
        # 检查事件类型
        if self.event_types and event.event_type not in self.event_types:
            return False
        
        # 检查源ID
        if self.source_ids and event.source_id not in self.source_ids:
            return False
        
        # 检查目标ID
        if self.target_ids and event.target_id not in self.target_ids:
            return False
        
        # 检查优先级
        if self.min_priority and event.priority.value < self.min_priority.value:
            return False
        
        # 检查自定义过滤器
        if self.custom_filter and not self.custom_filter(event):
            return False
        
        return True


class BaseEventHandler:
    """基础事件处理器"""
    
    def __init__(self, handler_id: str = None):
        self.handler_id = handler_id or str(uuid.uuid4())
        self.is_active = True
        self.processed_count = 0
        self.error_count = 0
        self.last_processing_time = 0.0
        
        # 性能统计
        self.total_processing_time = 0.0
        self.max_processing_time = 0.0
        self.min_processing_time = float('inf')
    
    def can_handle(self, event: BaseEvent) -> bool:
        """检查是否可以处理该事件"""
        return self.is_active
    
    def handle_event(self, event: BaseEvent) -> bool:
        """处理事件 - 子类需要重写"""
        try:
            event.mark_processing_start()
            
            # 调用具体的处理逻辑
            result = self.process_event(event)
            
            event.mark_processing_end(success=result)
            
            # 更新统计信息
            self.update_statistics(event)
            
            return result
            
        except Exception as e:
            event.mark_processing_end(success=False)
            self.error_count += 1
            error_msg = f"事件处理错误 {self.handler_id}: {e}"
            if logger:
                logger.log_err(error_msg)
            else:
                print(error_msg)
            return False
    
    def process_event(self, event: BaseEvent) -> bool:
        """具体的事件处理逻辑 - 子类重写"""
        return True
    
    def update_statistics(self, event: BaseEvent):
        """更新性能统计"""
        processing_time = event.get_processing_time()
        if processing_time:
            self.processed_count += 1
            self.total_processing_time += processing_time
            self.last_processing_time = processing_time
            
            if processing_time > self.max_processing_time:
                self.max_processing_time = processing_time
            
            if processing_time < self.min_processing_time:
                self.min_processing_time = processing_time
    
    def get_statistics(self) -> dict:
        """获取处理器统计信息"""
        avg_time = (self.total_processing_time / self.processed_count 
                   if self.processed_count > 0 else 0)
        
        return {
            'handler_id': self.handler_id,
            'processed_count': self.processed_count,
            'error_count': self.error_count,
            'average_processing_time': avg_time,
            'max_processing_time': self.max_processing_time,
            'min_processing_time': self.min_processing_time if self.min_processing_time != float('inf') else 0,
            'last_processing_time': self.last_processing_time,
            'is_active': self.is_active
        }


class AIDirectorEventHandler(BaseEventHandler):
    """AI导演事件处理器"""
    
    def __init__(self):
        super().__init__("ai_director_handler")
        self.narrative_queue = deque(maxlen=1000)
        self.decision_cache = {}
        self.context_window = {}
    
    def can_handle(self, event: BaseEvent) -> bool:
        """检查是否可以处理该事件"""
        if not super().can_handle(event):
            return False
        
        # AI导演处理高优先级事件和特定类型事件
        high_priority_events = {
            'CultivationBreakthroughEvent',
            'CelestialAnomalyEvent', 
            'CombatStateEvent',
            'SectConflictEvent',
            'AIDirectorNotificationEvent'
        }
        
        return (event.priority.value >= EventPriority.HIGH.value or 
                event.event_type in high_priority_events)
    
    def process_event(self, event: BaseEvent) -> bool:
        """处理AI导演相关事件"""
        try:
            # 添加到叙事队列
            self.narrative_queue.append(event)
            
            # 更新上下文窗口
            self.update_context_window(event)
            
            # 生成AI决策
            decision = self.generate_ai_decision(event)
            
            if decision:
                # 缓存决策
                self.decision_cache[event.event_id] = decision
                
                # 执行AI响应
                self.execute_ai_response(event, decision)
            
            return True
            
        except Exception as e:
            logger.log_err(f"AI导演事件处理失败: {e}")
            return False
    
    def update_context_window(self, event: BaseEvent):
        """更新AI上下文窗口"""
        character_id = event.source_id or event.target_id
        if character_id:
            if character_id not in self.context_window:
                self.context_window[character_id] = deque(maxlen=50)
            
            self.context_window[character_id].append({
                'event_type': event.event_type,
                'timestamp': event.timestamp,
                'data': event.data
            })
    
    def generate_ai_decision(self, event: BaseEvent) -> Optional[dict]:
        """生成AI决策"""
        # 这里会连接到实际的AI系统
        # 目前返回模拟决策
        
        decision_templates = {
            'CultivationBreakthroughEvent': {
                'type': 'narrative_response',
                'content': '天地灵气震荡，有人突破境界！',
                'trigger_probability': 0.8
            },
            'CelestialAnomalyEvent': {
                'type': 'world_event',
                'content': '天象异常，预示着重大变化即将到来...',
                'trigger_probability': 0.9
            },
            'CombatStateEvent': {
                'type': 'combat_enhancement',
                'content': '战斗激烈，引起周围修士关注',
                'trigger_probability': 0.6
            }
        }
        
        template = decision_templates.get(event.event_type)
        if template:
            import random
            if random.random() < template['trigger_probability']:
                return template
        
        return None
    
    def execute_ai_response(self, event: BaseEvent, decision: dict):
        """执行AI响应"""
        response_type = decision.get('type')
        content = decision.get('content')
        
        if response_type == 'narrative_response':
            # 发送叙事响应到相关角色
            self.send_narrative_message(event, content)
        
        elif response_type == 'world_event':
            # 触发世界事件
            self.trigger_world_event(event, content)
        
        elif response_type == 'combat_enhancement':
            # 增强战斗体验
            self.enhance_combat_experience(event, content)
    
    def send_narrative_message(self, event: BaseEvent, content: str):
        """发送叙事消息"""
        # 这里会发送消息到相关角色
        logger.log_info(f"AI叙事: {content}")
    
    def trigger_world_event(self, event: BaseEvent, content: str):
        """触发世界事件"""
        # 这里会触发世界级事件
        logger.log_info(f"世界事件: {content}")
    
    def enhance_combat_experience(self, event: BaseEvent, content: str):
        """增强战斗体验"""
        # 这里会增强战斗相关的体验
        if logger:
            logger.log_info(f"战斗增强: {content}")
        else:
            print(f"战斗增强: {content}")


class XianxiaEventBus(DefaultScript):
    """
    修仙事件总线 - 核心事件处理系统
    继承自DefaultScript以获得持久化和定时执行能力
    """

    _instance = None

    @classmethod
    def get_instance(cls):
        """获取事件总线单例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def at_script_creation(self):
        """脚本创建时初始化"""
        self.key = "xiuxian_event_bus"
        self.desc = "修仙MUD核心事件总线系统"
        self.interval = 0.1  # 100ms检查周期
        self.persistent = True
        self.start_delay = True

        # 事件队列 - 按优先级分组
        self.event_queues = {
            EventPriority.CRITICAL: deque(),
            EventPriority.HIGH: deque(),
            EventPriority.NORMAL: deque(),
            EventPriority.LOW: deque()
        }

        # 事件处理器注册表
        self.handlers: Dict[str, BaseEventHandler] = {}
        self.handler_filters: Dict[str, EventFilter] = {}

        # 性能统计
        self.stats = {
            'total_events_processed': 0,
            'events_per_second': 0.0,
            'average_processing_time': 0.0,
            'queue_sizes': {},
            'handler_performance': {},
            'last_stats_update': time.time()
        }

        # 事件历史 (用于调试和AI分析)
        self.event_history = deque(maxlen=10000)

        # 线程安全锁
        self.lock = threading.RLock()

        # 注册默认处理器
        self.register_default_handlers()

        logger.log_info("修仙事件总线系统已启动")

    def register_default_handlers(self):
        """注册默认事件处理器"""
        # 注册AI导演处理器
        ai_handler = AIDirectorEventHandler()
        self.register_handler("ai_director", ai_handler)

        # 为AI导演设置过滤器
        ai_filter = EventFilter(
            min_priority=EventPriority.HIGH,
            event_types=[
                'CultivationBreakthroughEvent',
                'CelestialAnomalyEvent',
                'CombatStateEvent',
                'SectConflictEvent',
                'AIDirectorNotificationEvent'
            ]
        )
        self.set_handler_filter("ai_director", ai_filter)

    def register_handler(self, handler_id: str, handler: BaseEventHandler,
                        event_filter: Optional[EventFilter] = None):
        """注册事件处理器"""
        with self.lock:
            self.handlers[handler_id] = handler
            if event_filter:
                self.handler_filters[handler_id] = event_filter

            logger.log_info(f"事件处理器已注册: {handler_id}")

    def unregister_handler(self, handler_id: str):
        """注销事件处理器"""
        with self.lock:
            if handler_id in self.handlers:
                del self.handlers[handler_id]
            if handler_id in self.handler_filters:
                del self.handler_filters[handler_id]

            logger.log_info(f"事件处理器已注销: {handler_id}")

    def set_handler_filter(self, handler_id: str, event_filter: EventFilter):
        """设置处理器过滤器"""
        with self.lock:
            self.handler_filters[handler_id] = event_filter

    def publish_event(self, event: BaseEvent) -> bool:
        """发布事件到总线"""
        try:
            with self.lock:
                # 添加到对应优先级队列
                self.event_queues[event.priority].append(event)

                # 添加到历史记录
                self.event_history.append(event)

                log_msg = f"事件已发布: {event.event_type} (ID: {event.event_id})"
                if logger:
                    logger.log_info(log_msg)
                else:
                    print(log_msg)
                return True

        except Exception as e:
            error_msg = f"事件发布失败: {e}"
            if logger:
                logger.log_err(error_msg)
            else:
                print(error_msg)
            return False

    def emit_event(self, event: BaseEvent) -> bool:
        """发送事件（publish_event的别名）"""
        return self.publish_event(event)

    def at_repeat(self):
        """定时执行 - 处理事件队列"""
        try:
            start_time = time.time()
            processed_count = 0

            # 按优先级处理事件
            for priority in [EventPriority.CRITICAL, EventPriority.HIGH,
                           EventPriority.NORMAL, EventPriority.LOW]:

                queue = self.event_queues[priority]
                batch_size = self.get_batch_size(priority)

                # 批量处理事件
                for _ in range(min(batch_size, len(queue))):
                    if queue:
                        event = queue.popleft()
                        if self.process_event(event):
                            processed_count += 1
                        else:
                            # 处理失败，检查是否可以重试
                            if event.can_retry():
                                event.increment_retry()
                                queue.append(event)  # 重新加入队列

            # 更新性能统计
            processing_time = time.time() - start_time
            self.update_performance_stats(processed_count, processing_time)

            # 定期清理和优化
            if time.time() - self.stats['last_stats_update'] > 60:  # 每分钟
                self.cleanup_and_optimize()

        except Exception as e:
            logger.log_err(f"事件总线处理错误: {e}")

    def get_batch_size(self, priority: EventPriority) -> int:
        """根据优先级获取批处理大小"""
        batch_sizes = {
            EventPriority.CRITICAL: 50,  # 紧急事件优先处理
            EventPriority.HIGH: 30,
            EventPriority.NORMAL: 20,
            EventPriority.LOW: 10
        }
        return batch_sizes.get(priority, 10)

    def process_event(self, event: BaseEvent) -> bool:
        """处理单个事件"""
        success = False

        with self.lock:
            # 找到匹配的处理器
            matching_handlers = self.find_matching_handlers(event)

            if not matching_handlers:
                logger.log_warn(f"没有找到匹配的处理器: {event.event_type}")
                return False

            # 并行处理 (对于可以并行的处理器)
            for handler_id, handler in matching_handlers:
                try:
                    if handler.handle_event(event):
                        success = True
                except Exception as e:
                    logger.log_err(f"处理器 {handler_id} 处理事件失败: {e}")

        return success

    def find_matching_handlers(self, event: BaseEvent) -> List[tuple]:
        """找到匹配的事件处理器"""
        matching_handlers = []

        for handler_id, handler in self.handlers.items():
            # 检查处理器是否可以处理该事件
            if not handler.can_handle(event):
                continue

            # 检查过滤器
            event_filter = self.handler_filters.get(handler_id)
            if event_filter and not event_filter.matches(event):
                continue

            matching_handlers.append((handler_id, handler))

        return matching_handlers

    def update_performance_stats(self, processed_count: int, processing_time: float):
        """更新性能统计"""
        current_time = time.time()

        # 更新基本统计
        self.stats['total_events_processed'] += processed_count

        # 计算每秒事件处理数
        time_diff = current_time - self.stats['last_stats_update']
        if time_diff > 0:
            self.stats['events_per_second'] = processed_count / time_diff

        # 更新平均处理时间
        if processed_count > 0:
            self.stats['average_processing_time'] = processing_time / processed_count

        # 更新队列大小统计
        self.stats['queue_sizes'] = {
            priority.name: len(queue)
            for priority, queue in self.event_queues.items()
        }

        # 更新处理器性能统计
        self.stats['handler_performance'] = {
            handler_id: handler.get_statistics()
            for handler_id, handler in self.handlers.items()
        }

    def cleanup_and_optimize(self):
        """清理和优化"""
        current_time = time.time()

        # 清理过期事件历史
        cutoff_time = current_time - 3600  # 保留1小时历史
        while (self.event_history and
               self.event_history[0].timestamp < cutoff_time):
            self.event_history.popleft()

        # 清理失败的事件
        for priority, queue in self.event_queues.items():
            failed_events = []
            remaining_events = deque()

            while queue:
                event = queue.popleft()
                if (event.status == EventStatus.FAILED and
                    not event.can_retry()):
                    failed_events.append(event)
                else:
                    remaining_events.append(event)

            # 重新填充队列
            self.event_queues[priority] = remaining_events

            if failed_events:
                logger.log_warn(f"清理了 {len(failed_events)} 个失败事件 (优先级: {priority.name})")

        # 更新统计时间
        self.stats['last_stats_update'] = current_time

        logger.log_info("事件总线清理和优化完成")

    def get_system_status(self) -> dict:
        """获取系统状态"""
        with self.lock:
            total_queued = sum(len(queue) for queue in self.event_queues.values())

            return {
                'is_running': self.is_active,
                'total_events_processed': self.stats['total_events_processed'],
                'events_per_second': self.stats['events_per_second'],
                'average_processing_time': self.stats['average_processing_time'],
                'total_queued_events': total_queued,
                'queue_sizes': self.stats['queue_sizes'],
                'registered_handlers': list(self.handlers.keys()),
                'handler_performance': self.stats['handler_performance'],
                'event_history_size': len(self.event_history)
            }

    def force_process_queue(self, priority: EventPriority = None):
        """强制处理指定优先级的队列"""
        if priority:
            priorities = [priority]
        else:
            priorities = [EventPriority.CRITICAL, EventPriority.HIGH,
                         EventPriority.NORMAL, EventPriority.LOW]

        processed_count = 0
        for p in priorities:
            queue = self.event_queues[p]
            while queue:
                event = queue.popleft()
                if self.process_event(event):
                    processed_count += 1

        logger.log_info(f"强制处理了 {processed_count} 个事件")
        return processed_count

    def get_event_history(self, event_type: str = None,
                         limit: int = 100) -> List[dict]:
        """获取事件历史"""
        history = []
        count = 0

        # 从最新的事件开始
        for event in reversed(self.event_history):
            if count >= limit:
                break

            if event_type is None or event.event_type == event_type:
                history.append(event.to_dict())
                count += 1

        return history

    def at_script_shutdown(self):
        """脚本关闭时清理"""
        logger.log_info("修仙事件总线系统正在关闭...")

        # 处理剩余的高优先级事件
        self.force_process_queue(EventPriority.CRITICAL)
        self.force_process_queue(EventPriority.HIGH)

        # 保存统计信息
        final_stats = self.get_system_status()
        logger.log_info(f"事件总线最终统计: {final_stats}")

        logger.log_info("修仙事件总线系统已关闭")


# 便利函数
def get_event_bus() -> Optional[XianxiaEventBus]:
    """获取事件总线实例"""
    from evennia import search_script
    scripts = search_script("xiuxian_event_bus")
    return scripts[0] if scripts else None


def publish_event(event: BaseEvent) -> bool:
    """发布事件的便利函数"""
    event_bus = get_event_bus()
    if event_bus:
        return event_bus.publish_event(event)
    else:
        logger.log_err("事件总线未找到，无法发布事件")
        return False


def create_event_bus() -> XianxiaEventBus:
    """创建事件总线实例"""
    return create.create_script(XianxiaEventBus)

# 炼丹Handler - 管理角色炼丹相关功能
# 基于设计文档实现的高性能炼丹系统

import time
import random
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from evennia import logger
from ..handler_system import BaseHandler, handler_method, HandlerRegistry
from ..event_system import XianxiaEventBus


class PillGrade(Enum):
    """丹药品级"""
    MORTAL = "凡品"      # 凡品
    SPIRIT = "灵品"      # 灵品  
    TREASURE = "宝品"    # 宝品
    KING = "王品"        # 王品
    EMPEROR = "帝品"     # 帝品
    SAINT = "圣品"       # 圣品


class PillType(Enum):
    """丹药类型"""
    CULTIVATION = "修炼"  # 修炼类
    HEALING = "治疗"      # 治疗类
    ENHANCEMENT = "增强"  # 增强类
    SPECIAL = "特殊"      # 特殊类


@dataclass
class Ingredient:
    """炼丹材料"""
    name: str
    grade: PillGrade
    type: str
    properties: Dict[str, Any]
    rarity: int  # 稀有度 1-10


@dataclass
class PillRecipe:
    """丹药配方"""
    name: str
    grade: PillGrade
    pill_type: PillType
    ingredients: List[Tuple[str, int]]  # (材料名, 数量)
    success_rate: float
    alchemy_level_required: int
    effects: Dict[str, Any]
    description: str


@dataclass
class Pill:
    """丹药"""
    name: str
    grade: PillGrade
    pill_type: PillType
    quality: int  # 品质 1-100
    effects: Dict[str, Any]
    created_time: float
    creator_id: str


class AlchemyHandler(BaseHandler):
    """
    炼丹Handler - 管理角色的炼丹技能、丹药制作、材料管理等
    """
    
    def __init__(self, owner):
        super().__init__(owner)
        
        # 炼丹等级和经验
        self.alchemy_level = 1
        self.alchemy_experience = 0
        
        # 配方库
        self.recipe_library = {}
        self.known_recipes = set()
        
        # 材料库存
        self.ingredient_storage = {}  # ingredient_name -> quantity
        
        # 炼丹状态
        self.alchemy_state = {
            "is_refining": False,
            "current_recipe": None,
            "refining_start_time": 0,
            "refining_duration": 0,
            "success_modifiers": {}
        }
        
        # 丹药库存
        self.pill_storage = []
        
        # 事件总线
        self.event_bus = None
        
        # 加载默认配方和材料
        self.load_default_recipes()
        self.load_default_ingredients()
    
    def initialize(self):
        """初始化炼丹Handler"""
        super().initialize()
        
        # 获取事件总线
        self.event_bus = XianxiaEventBus.get_instance()
        
        # 学习基础配方
        self.learn_basic_recipes()
        
        # 给予一些基础材料
        self.add_starter_ingredients()
        
        self.log_info("炼丹Handler初始化完成")
    
    def load_default_recipes(self):
        """加载默认配方"""
        default_recipes = [
            PillRecipe(
                name="回气丹",
                grade=PillGrade.MORTAL,
                pill_type=PillType.CULTIVATION,
                ingredients=[("灵草", 2), ("清水", 1)],
                success_rate=0.8,
                alchemy_level_required=1,
                effects={"spiritual_energy_restore": 50},
                description="恢复灵力的基础丹药"
            ),
            
            PillRecipe(
                name="疗伤丹",
                grade=PillGrade.MORTAL,
                pill_type=PillType.HEALING,
                ingredients=[("血参", 1), ("止血草", 2)],
                success_rate=0.7,
                alchemy_level_required=2,
                effects={"health_restore": 100, "heal_over_time": 20},
                description="治疗外伤的丹药"
            ),
            
            PillRecipe(
                name="筑基丹",
                grade=PillGrade.SPIRIT,
                pill_type=PillType.CULTIVATION,
                ingredients=[("千年人参", 1), ("灵芝", 1), ("龙血草", 1)],
                success_rate=0.5,
                alchemy_level_required=5,
                effects={"breakthrough_chance": 0.3, "cultivation_boost": 1000},
                description="帮助筑基期突破的珍贵丹药"
            ),
            
            PillRecipe(
                name="金丹",
                grade=PillGrade.TREASURE,
                pill_type=PillType.CULTIVATION,
                ingredients=[("金莲子", 1), ("九转还魂草", 1), ("天材地宝", 1)],
                success_rate=0.3,
                alchemy_level_required=10,
                effects={"major_breakthrough": True, "cultivation_boost": 5000},
                description="金丹期修士梦寐以求的丹药"
            ),
            
            PillRecipe(
                name="力量丹",
                grade=PillGrade.MORTAL,
                pill_type=PillType.ENHANCEMENT,
                ingredients=[("虎骨", 1), ("熊胆", 1)],
                success_rate=0.6,
                alchemy_level_required=3,
                effects={"strength_boost": 0.2, "duration": 3600},
                description="临时增强力量的丹药"
            )
        ]
        
        for recipe in default_recipes:
            self.recipe_library[recipe.name] = recipe
    
    def load_default_ingredients(self):
        """加载默认材料库"""
        self.ingredient_library = {
            "灵草": Ingredient("灵草", PillGrade.MORTAL, "草药", {"energy": 10}, 1),
            "清水": Ingredient("清水", PillGrade.MORTAL, "液体", {"purity": 5}, 1),
            "血参": Ingredient("血参", PillGrade.MORTAL, "草药", {"healing": 20}, 2),
            "止血草": Ingredient("止血草", PillGrade.MORTAL, "草药", {"healing": 15}, 1),
            "千年人参": Ingredient("千年人参", PillGrade.SPIRIT, "草药", {"energy": 100}, 6),
            "灵芝": Ingredient("灵芝", PillGrade.SPIRIT, "菌类", {"longevity": 50}, 5),
            "龙血草": Ingredient("龙血草", PillGrade.SPIRIT, "草药", {"power": 80}, 7),
            "金莲子": Ingredient("金莲子", PillGrade.TREASURE, "种子", {"enlightenment": 200}, 8),
            "九转还魂草": Ingredient("九转还魂草", PillGrade.TREASURE, "草药", {"revival": 300}, 9),
            "天材地宝": Ingredient("天材地宝", PillGrade.TREASURE, "矿物", {"essence": 500}, 10),
            "虎骨": Ingredient("虎骨", PillGrade.MORTAL, "骨骼", {"strength": 30}, 3),
            "熊胆": Ingredient("熊胆", PillGrade.MORTAL, "内脏", {"vitality": 25}, 3)
        }
    
    def learn_basic_recipes(self):
        """学习基础配方"""
        basic_recipes = ["回气丹", "疗伤丹"]
        for recipe_name in basic_recipes:
            if recipe_name in self.recipe_library:
                self.known_recipes.add(recipe_name)
    
    def add_starter_ingredients(self):
        """添加初始材料"""
        starter_materials = {
            "灵草": 10,
            "清水": 5,
            "血参": 3,
            "止血草": 5
        }
        
        for ingredient, quantity in starter_materials.items():
            self.ingredient_storage[ingredient] = quantity
    
    @handler_method
    def learn_recipe(self, recipe_name: str) -> dict:
        """学习新配方"""
        if recipe_name not in self.recipe_library:
            return {"success": False, "message": f"配方 {recipe_name} 不存在"}
        
        if recipe_name in self.known_recipes:
            return {"success": False, "message": f"已经学会配方 {recipe_name}"}
        
        recipe = self.recipe_library[recipe_name]
        
        # 检查炼丹等级要求
        if self.alchemy_level < recipe.alchemy_level_required:
            return {
                "success": False, 
                "message": f"炼丹等级不足，需要 {recipe.alchemy_level_required} 级，当前 {self.alchemy_level} 级"
            }
        
        # 学习配方
        self.known_recipes.add(recipe_name)
        
        self.log_info(f"学会新配方: {recipe_name}")
        
        return {
            "success": True,
            "message": f"成功学会配方: {recipe_name}",
            "recipe_info": self.get_recipe_info(recipe_name)
        }
    
    @handler_method
    def start_refining(self, recipe_name: str) -> dict:
        """开始炼丹"""
        if self.alchemy_state["is_refining"]:
            return {"success": False, "message": "正在炼丹中，无法开始新的炼制"}
        
        if recipe_name not in self.known_recipes:
            return {"success": False, "message": f"未学会配方 {recipe_name}"}
        
        recipe = self.recipe_library[recipe_name]
        
        # 检查材料是否充足
        missing_ingredients = []
        for ingredient_name, required_quantity in recipe.ingredients:
            current_quantity = self.ingredient_storage.get(ingredient_name, 0)
            if current_quantity < required_quantity:
                missing_ingredients.append(f"{ingredient_name}(需要{required_quantity}，拥有{current_quantity})")
        
        if missing_ingredients:
            return {
                "success": False, 
                "message": f"材料不足: {', '.join(missing_ingredients)}"
            }
        
        # 消耗材料
        for ingredient_name, required_quantity in recipe.ingredients:
            self.ingredient_storage[ingredient_name] -= required_quantity
        
        # 计算炼制时间 (基于丹药品级)
        base_time = {
            PillGrade.MORTAL: 60,    # 1分钟
            PillGrade.SPIRIT: 300,   # 5分钟
            PillGrade.TREASURE: 900, # 15分钟
            PillGrade.KING: 1800,    # 30分钟
            PillGrade.EMPEROR: 3600, # 1小时
            PillGrade.SAINT: 7200    # 2小时
        }
        
        refining_duration = base_time.get(recipe.grade, 60)
        
        # 开始炼制
        self.alchemy_state.update({
            "is_refining": True,
            "current_recipe": recipe,
            "refining_start_time": time.time(),
            "refining_duration": refining_duration,
            "success_modifiers": self.calculate_success_modifiers(recipe)
        })
        
        self.log_info(f"开始炼制: {recipe_name}, 预计时间: {refining_duration}秒")
        
        return {
            "success": True,
            "recipe_name": recipe_name,
            "refining_duration": refining_duration,
            "estimated_finish_time": time.time() + refining_duration
        }
    
    @handler_method
    def check_refining_progress(self) -> dict:
        """检查炼丹进度"""
        if not self.alchemy_state["is_refining"]:
            return {"success": False, "message": "当前没有在炼丹"}
        
        current_time = time.time()
        elapsed_time = current_time - self.alchemy_state["refining_start_time"]
        total_time = self.alchemy_state["refining_duration"]
        
        if elapsed_time >= total_time:
            # 炼制完成
            return self.finish_refining()
        else:
            # 炼制中
            progress = (elapsed_time / total_time) * 100
            remaining_time = total_time - elapsed_time
            
            return {
                "success": True,
                "status": "refining",
                "progress": f"{progress:.1f}%",
                "remaining_time": remaining_time,
                "recipe_name": self.alchemy_state["current_recipe"].name
            }
    
    def finish_refining(self) -> dict:
        """完成炼丹"""
        recipe = self.alchemy_state["current_recipe"]
        success_modifiers = self.alchemy_state["success_modifiers"]
        
        # 计算成功率
        base_success_rate = recipe.success_rate
        level_bonus = (self.alchemy_level - recipe.alchemy_level_required) * 0.05
        modifier_bonus = sum(success_modifiers.values())
        
        final_success_rate = min(base_success_rate + level_bonus + modifier_bonus, 0.95)
        
        # 判定成功
        success = random.random() < final_success_rate
        
        # 重置炼丹状态
        self.alchemy_state.update({
            "is_refining": False,
            "current_recipe": None,
            "refining_start_time": 0,
            "refining_duration": 0,
            "success_modifiers": {}
        })
        
        if success:
            # 炼制成功
            quality = self.calculate_pill_quality(recipe, final_success_rate)
            
            pill = Pill(
                name=recipe.name,
                grade=recipe.grade,
                pill_type=recipe.pill_type,
                quality=quality,
                effects=recipe.effects.copy(),
                created_time=time.time(),
                creator_id=str(self.get_owner().id) if self.get_owner() else "unknown"
            )
            
            # 根据品质调整效果
            for effect_name, effect_value in pill.effects.items():
                if isinstance(effect_value, (int, float)):
                    pill.effects[effect_name] = effect_value * (quality / 100)
            
            self.pill_storage.append(pill)
            
            # 增加炼丹经验
            exp_gained = self.calculate_experience_gain(recipe, quality)
            self.add_alchemy_experience(exp_gained)
            
            result = {
                "success": True,
                "message": f"炼制成功！获得 {recipe.name}",
                "pill": {
                    "name": pill.name,
                    "grade": pill.grade.value,
                    "quality": quality,
                    "effects": pill.effects
                },
                "experience_gained": exp_gained,
                "success_rate": f"{final_success_rate*100:.1f}%"
            }
            
            self.log_info(f"炼丹成功: {recipe.name}, 品质: {quality}")
            
        else:
            # 炼制失败
            # 增加少量经验
            exp_gained = recipe.alchemy_level_required
            self.add_alchemy_experience(exp_gained)
            
            result = {
                "success": False,
                "message": f"炼制失败！{recipe.name} 炼制失败",
                "experience_gained": exp_gained,
                "success_rate": f"{final_success_rate*100:.1f}%"
            }
            
            self.log_info(f"炼丹失败: {recipe.name}")
        
        return result
    
    def calculate_success_modifiers(self, recipe: PillRecipe) -> Dict[str, float]:
        """计算成功率修正"""
        modifiers = {}
        
        # 炼丹等级加成
        level_diff = self.alchemy_level - recipe.alchemy_level_required
        if level_diff > 0:
            modifiers["level_bonus"] = level_diff * 0.02
        
        # 材料品质加成 (简化实现)
        modifiers["material_quality"] = 0.05
        
        return modifiers
    
    def calculate_pill_quality(self, recipe: PillRecipe, success_rate: float) -> int:
        """计算丹药品质"""
        base_quality = 60
        success_bonus = (success_rate - recipe.success_rate) * 100
        level_bonus = (self.alchemy_level - recipe.alchemy_level_required) * 2
        random_factor = random.randint(-10, 20)
        
        quality = int(base_quality + success_bonus + level_bonus + random_factor)
        return max(1, min(100, quality))
    
    def calculate_experience_gain(self, recipe: PillRecipe, quality: int) -> int:
        """计算经验获得"""
        base_exp = recipe.alchemy_level_required * 10
        quality_bonus = quality // 10
        grade_multiplier = {
            PillGrade.MORTAL: 1.0,
            PillGrade.SPIRIT: 1.5,
            PillGrade.TREASURE: 2.0,
            PillGrade.KING: 3.0,
            PillGrade.EMPEROR: 4.0,
            PillGrade.SAINT: 5.0
        }
        
        multiplier = grade_multiplier.get(recipe.grade, 1.0)
        return int((base_exp + quality_bonus) * multiplier)
    
    def add_alchemy_experience(self, exp: int):
        """增加炼丹经验"""
        self.alchemy_experience += exp
        
        # 检查是否升级
        while self.alchemy_experience >= self.get_level_up_requirement():
            self.alchemy_experience -= self.get_level_up_requirement()
            self.alchemy_level += 1
            self.log_info(f"炼丹等级提升至 {self.alchemy_level} 级")
    
    def get_level_up_requirement(self) -> int:
        """获取升级所需经验"""
        return self.alchemy_level * 100
    
    @handler_method
    def add_ingredient(self, ingredient_name: str, quantity: int):
        """添加材料"""
        if ingredient_name in self.ingredient_storage:
            self.ingredient_storage[ingredient_name] += quantity
        else:
            self.ingredient_storage[ingredient_name] = quantity
    
    @handler_method
    def use_pill(self, pill_index: int) -> dict:
        """使用丹药"""
        if pill_index < 0 or pill_index >= len(self.pill_storage):
            return {"success": False, "message": "丹药不存在"}
        
        pill = self.pill_storage.pop(pill_index)
        
        # 应用丹药效果
        owner = self.get_owner()
        if owner:
            self.apply_pill_effects(owner, pill)
        
        self.log_info(f"使用丹药: {pill.name}")
        
        return {
            "success": True,
            "message": f"使用了 {pill.name}",
            "effects": pill.effects
        }
    
    def apply_pill_effects(self, owner, pill: Pill):
        """应用丹药效果"""
        for effect_name, effect_value in pill.effects.items():
            if effect_name == "spiritual_energy_restore" and hasattr(owner, 'cultivation'):
                current = owner.cultivation.get("spiritual_energy", 0)
                owner.cultivation.set("spiritual_energy", current + effect_value)
            
            elif effect_name == "cultivation_boost" and hasattr(owner, 'cultivation'):
                owner.cultivation.advance_cultivation(effect_value)
            
            elif effect_name == "breakthrough_chance" and hasattr(owner, 'cultivation'):
                # 临时增加突破几率 (简化实现)
                owner.cultivation.set("temp_breakthrough_bonus", effect_value)
    
    @handler_method
    def get_known_recipes(self) -> List[dict]:
        """获取已知配方"""
        recipes = []
        
        for recipe_name in self.known_recipes:
            if recipe_name in self.recipe_library:
                recipe_info = self.get_recipe_info(recipe_name)
                recipes.append(recipe_info)
        
        return recipes
    
    @handler_method
    def get_ingredient_storage(self) -> Dict[str, int]:
        """获取材料库存"""
        return self.ingredient_storage.copy()
    
    @handler_method
    def get_pill_storage(self) -> List[dict]:
        """获取丹药库存"""
        pills = []
        
        for i, pill in enumerate(self.pill_storage):
            pills.append({
                "index": i,
                "name": pill.name,
                "grade": pill.grade.value,
                "type": pill.pill_type.value,
                "quality": pill.quality,
                "effects": pill.effects,
                "created_time": pill.created_time
            })
        
        return pills
    
    def get_recipe_info(self, recipe_name: str) -> dict:
        """获取配方信息"""
        if recipe_name not in self.recipe_library:
            return {}
        
        recipe = self.recipe_library[recipe_name]
        
        return {
            "name": recipe.name,
            "grade": recipe.grade.value,
            "type": recipe.pill_type.value,
            "ingredients": recipe.ingredients,
            "success_rate": recipe.success_rate,
            "level_required": recipe.alchemy_level_required,
            "effects": recipe.effects,
            "description": recipe.description
        }
    
    @handler_method
    def get_alchemy_status(self) -> dict:
        """获取炼丹状态"""
        return {
            "alchemy_level": self.alchemy_level,
            "alchemy_experience": self.alchemy_experience,
            "experience_to_next_level": self.get_level_up_requirement() - self.alchemy_experience,
            "known_recipes_count": len(self.known_recipes),
            "ingredient_types_count": len(self.ingredient_storage),
            "pills_count": len(self.pill_storage),
            "is_refining": self.alchemy_state["is_refining"]
        }


# 注册Handler
HandlerRegistry.register(AlchemyHandler, "alchemy")

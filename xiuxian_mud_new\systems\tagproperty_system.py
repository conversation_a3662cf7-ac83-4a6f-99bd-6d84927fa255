# TagProperty高性能查询系统
# 基于设计文档实现的10-100x性能提升查询系统

import time
import threading
from typing import Dict, List, Any, Optional, Union, Set, Tuple
from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum
import weakref
import bisect

from evennia import logger
from evennia.utils.utils import class_from_module


class IndexType(Enum):
    """索引类型"""
    PRIMARY = "primary"      # 主键索引
    VALUE = "value"          # 值索引
    RANGE = "range"          # 范围索引
    COMPOSITE = "composite"  # 复合索引
    CATEGORY = "category"    # 分类索引


@dataclass
class IndexEntry:
    """索引条目"""
    object_id: str
    tag_name: str
    tag_value: Any
    timestamp: float = field(default_factory=time.time)


class TagIndexManager:
    """
    TagProperty索引管理器 - 单例模式
    提供高性能的标签查询和索引管理
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        # 索引存储
        self.indexes: Dict[IndexType, Dict[str, Any]] = {
            IndexType.PRIMARY: {},      # object_id -> {tag_name: tag_value}
            IndexType.VALUE: defaultdict(set),  # tag_value -> {object_ids}
            IndexType.RANGE: defaultdict(list), # tag_name -> [(value, object_id)]
            IndexType.COMPOSITE: {},    # (tag1, tag2, ...) -> {object_ids}
            IndexType.CATEGORY: defaultdict(lambda: defaultdict(set))  # category -> tag_name -> {object_ids}
        }
        
        # 性能统计
        self.stats = {
            'total_queries': 0,
            'cache_hits': 0,
            'index_updates': 0,
            'average_query_time': 0.0,
            'last_cleanup': time.time()
        }
        
        # 查询缓存
        self.query_cache: Dict[str, Tuple[Any, float]] = {}
        self.cache_ttl = 300  # 5分钟缓存
        
        # 线程安全锁
        self.rw_lock = threading.RLock()
        
        self._initialized = True
        logger.log_info("TagProperty索引管理器已初始化")
    
    def add_tag(self, object_id: str, tag_name: str, tag_value: Any, 
                category: str = "default"):
        """添加标签到索引"""
        with self.rw_lock:
            try:
                # 更新主键索引
                if object_id not in self.indexes[IndexType.PRIMARY]:
                    self.indexes[IndexType.PRIMARY][object_id] = {}
                self.indexes[IndexType.PRIMARY][object_id][tag_name] = tag_value
                
                # 更新值索引
                self.indexes[IndexType.VALUE][tag_value].add(object_id)
                
                # 更新范围索引 (仅对数值类型)
                if isinstance(tag_value, (int, float)):
                    range_list = self.indexes[IndexType.RANGE][tag_name]
                    # 使用二分查找插入，保持排序
                    bisect.insort(range_list, (tag_value, object_id))
                
                # 更新分类索引
                self.indexes[IndexType.CATEGORY][category][tag_name].add(object_id)
                
                # 清理相关缓存
                self.invalidate_cache_for_tag(tag_name)
                
                self.stats['index_updates'] += 1
                
            except Exception as e:
                logger.log_err(f"添加标签索引失败: {e}")
    
    def remove_tag(self, object_id: str, tag_name: str, category: str = "default"):
        """从索引中移除标签"""
        with self.rw_lock:
            try:
                # 从主键索引获取旧值
                old_value = None
                if (object_id in self.indexes[IndexType.PRIMARY] and 
                    tag_name in self.indexes[IndexType.PRIMARY][object_id]):
                    old_value = self.indexes[IndexType.PRIMARY][object_id][tag_name]
                    del self.indexes[IndexType.PRIMARY][object_id][tag_name]
                
                # 从值索引移除
                if old_value is not None:
                    self.indexes[IndexType.VALUE][old_value].discard(object_id)
                    if not self.indexes[IndexType.VALUE][old_value]:
                        del self.indexes[IndexType.VALUE][old_value]
                
                # 从范围索引移除
                if isinstance(old_value, (int, float)):
                    range_list = self.indexes[IndexType.RANGE][tag_name]
                    try:
                        range_list.remove((old_value, object_id))
                    except ValueError:
                        pass
                
                # 从分类索引移除
                self.indexes[IndexType.CATEGORY][category][tag_name].discard(object_id)
                
                # 清理相关缓存
                self.invalidate_cache_for_tag(tag_name)
                
                self.stats['index_updates'] += 1
                
            except Exception as e:
                logger.log_err(f"移除标签索引失败: {e}")
    
    def query_by_value(self, tag_value: Any) -> Set[str]:
        """按值查询对象ID"""
        cache_key = f"value:{tag_value}"
        
        # 检查缓存
        cached_result = self.get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result
        
        start_time = time.time()
        
        with self.rw_lock:
            result = self.indexes[IndexType.VALUE].get(tag_value, set()).copy()
        
        # 更新统计和缓存
        self.update_query_stats(time.time() - start_time)
        self.cache_result(cache_key, result)
        
        return result
    
    def query_by_range(self, tag_name: str, min_value: Union[int, float], 
                      max_value: Union[int, float]) -> Set[str]:
        """按范围查询对象ID"""
        cache_key = f"range:{tag_name}:{min_value}:{max_value}"
        
        # 检查缓存
        cached_result = self.get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result
        
        start_time = time.time()
        
        with self.rw_lock:
            range_list = self.indexes[IndexType.RANGE].get(tag_name, [])
            result = set()
            
            # 使用二分查找优化范围查询
            start_idx = bisect.bisect_left(range_list, (min_value, ""))
            end_idx = bisect.bisect_right(range_list, (max_value, "~"))
            
            for i in range(start_idx, end_idx):
                if i < len(range_list):
                    value, object_id = range_list[i]
                    if min_value <= value <= max_value:
                        result.add(object_id)
        
        # 更新统计和缓存
        self.update_query_stats(time.time() - start_time)
        self.cache_result(cache_key, result)
        
        return result
    
    def query_by_category(self, category: str, tag_name: str = None) -> Set[str]:
        """按分类查询对象ID"""
        cache_key = f"category:{category}:{tag_name}"
        
        # 检查缓存
        cached_result = self.get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result
        
        start_time = time.time()
        
        with self.rw_lock:
            if tag_name:
                result = self.indexes[IndexType.CATEGORY][category][tag_name].copy()
            else:
                # 返回该分类下所有对象
                result = set()
                for tag_objects in self.indexes[IndexType.CATEGORY][category].values():
                    result.update(tag_objects)
        
        # 更新统计和缓存
        self.update_query_stats(time.time() - start_time)
        self.cache_result(cache_key, result)
        
        return result
    
    def get_object_tags(self, object_id: str) -> Dict[str, Any]:
        """获取对象的所有标签"""
        with self.rw_lock:
            return self.indexes[IndexType.PRIMARY].get(object_id, {}).copy()
    
    def get_cached_result(self, cache_key: str) -> Optional[Set[str]]:
        """获取缓存结果"""
        if cache_key in self.query_cache:
            result, timestamp = self.query_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                self.stats['cache_hits'] += 1
                return result.copy() if isinstance(result, set) else result
            else:
                del self.query_cache[cache_key]
        return None
    
    def cache_result(self, cache_key: str, result: Any):
        """缓存查询结果"""
        self.query_cache[cache_key] = (result.copy() if isinstance(result, set) else result, time.time())
        
        # 限制缓存大小
        if len(self.query_cache) > 10000:
            self.cleanup_cache()
    
    def invalidate_cache_for_tag(self, tag_name: str):
        """清理与特定标签相关的缓存"""
        keys_to_remove = []
        for cache_key in self.query_cache:
            if tag_name in cache_key:
                keys_to_remove.append(cache_key)
        
        for key in keys_to_remove:
            del self.query_cache[key]
    
    def cleanup_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        keys_to_remove = []
        
        for cache_key, (result, timestamp) in self.query_cache.items():
            if current_time - timestamp >= self.cache_ttl:
                keys_to_remove.append(cache_key)
        
        for key in keys_to_remove:
            del self.query_cache[key]
        
        # 如果缓存仍然太大，移除最老的条目
        if len(self.query_cache) > 5000:
            sorted_items = sorted(self.query_cache.items(), 
                                key=lambda x: x[1][1])  # 按时间戳排序
            for i in range(len(sorted_items) // 2):  # 移除一半
                del self.query_cache[sorted_items[i][0]]
    
    def update_query_stats(self, query_time: float):
        """更新查询统计"""
        self.stats['total_queries'] += 1
        
        # 计算平均查询时间
        total_time = (self.stats['average_query_time'] * (self.stats['total_queries'] - 1) + 
                     query_time)
        self.stats['average_query_time'] = total_time / self.stats['total_queries']
    
    def get_performance_stats(self) -> dict:
        """获取性能统计"""
        with self.rw_lock:
            cache_hit_rate = (self.stats['cache_hits'] / max(self.stats['total_queries'], 1) * 100)
            
            return {
                'total_queries': self.stats['total_queries'],
                'cache_hits': self.stats['cache_hits'],
                'cache_hit_rate': f"{cache_hit_rate:.2f}%",
                'index_updates': self.stats['index_updates'],
                'average_query_time': f"{self.stats['average_query_time']*1000:.2f}ms",
                'cache_size': len(self.query_cache),
                'indexed_objects': len(self.indexes[IndexType.PRIMARY]),
                'index_sizes': {
                    'primary': len(self.indexes[IndexType.PRIMARY]),
                    'value': len(self.indexes[IndexType.VALUE]),
                    'range': sum(len(v) for v in self.indexes[IndexType.RANGE].values()),
                    'category': sum(len(v) for cat in self.indexes[IndexType.CATEGORY].values() 
                                  for v in cat.values())
                }
            }


class TagQuery:
    """TagProperty查询接口"""

    def __init__(self, index_manager: TagIndexManager = None):
        self.index_manager = index_manager or TagIndexManager()

    def find_by_value(self, tag_value: Any) -> Set[str]:
        """按值查找对象"""
        return self.index_manager.query_by_value(tag_value)

    def find_by_range(self, tag_name: str, min_value: Union[int, float],
                     max_value: Union[int, float]) -> Set[str]:
        """按范围查找对象"""
        return self.index_manager.query_by_range(tag_name, min_value, max_value)

    def find_by_category(self, category: str, tag_name: str = None) -> Set[str]:
        """按分类查找对象"""
        return self.index_manager.query_by_category(category, tag_name)

    def find_intersection(self, *queries) -> Set[str]:
        """查找多个查询结果的交集"""
        if not queries:
            return set()

        result = queries[0]
        for query in queries[1:]:
            result = result.intersection(query)

        return result

    def find_union(self, *queries) -> Set[str]:
        """查找多个查询结果的并集"""
        result = set()
        for query in queries:
            result = result.union(query)

        return result

    def complex_query(self, conditions: List[dict]) -> Set[str]:
        """复杂查询"""
        results = []

        for condition in conditions:
            query_type = condition.get('type')

            if query_type == 'value':
                result = self.find_by_value(condition['value'])
            elif query_type == 'range':
                result = self.find_by_range(
                    condition['tag_name'],
                    condition['min_value'],
                    condition['max_value']
                )
            elif query_type == 'category':
                result = self.find_by_category(
                    condition['category'],
                    condition.get('tag_name')
                )
            else:
                continue

            results.append(result)

        # 默认使用交集
        operation = conditions[0].get('operation', 'intersection')
        if operation == 'intersection':
            return self.find_intersection(*results)
        elif operation == 'union':
            return self.find_union(*results)
        else:
            return results[0] if results else set()


class TagProperty:
    """
    TagProperty核心类 - 高性能标签属性系统
    """

    def __init__(self, owner, category: str = "default"):
        self.owner = weakref.ref(owner) if owner else None
        self.category = category
        self.index_manager = TagIndexManager()
        self.query = TagQuery(self.index_manager)

        # 本地标签缓存
        self._local_tags: Dict[str, Any] = {}
        self._dirty_tags: Set[str] = set()

        # 性能统计
        self.stats = {
            'get_operations': 0,
            'set_operations': 0,
            'query_operations': 0,
            'cache_hits': 0
        }

    def get_owner_id(self) -> str:
        """获取拥有者ID"""
        owner = self.owner() if self.owner else None
        return str(owner.id) if owner else "unknown"

    def set(self, tag_name: str, tag_value: Any, persist: bool = True):
        """设置标签值"""
        try:
            owner_id = self.get_owner_id()

            # 更新本地缓存
            old_value = self._local_tags.get(tag_name)
            self._local_tags[tag_name] = tag_value

            # 如果值发生变化，更新索引
            if old_value != tag_value:
                # 移除旧索引
                if old_value is not None:
                    self.index_manager.remove_tag(owner_id, tag_name, self.category)

                # 添加新索引
                self.index_manager.add_tag(owner_id, tag_name, tag_value, self.category)

                # 标记为脏数据
                self._dirty_tags.add(tag_name)

            # 持久化到数据库
            if persist:
                self.persist_tag(tag_name, tag_value)

            self.stats['set_operations'] += 1

        except Exception as e:
            logger.log_err(f"设置标签失败: {e}")

    def get(self, tag_name: str, default: Any = None) -> Any:
        """获取标签值"""
        try:
            # 首先检查本地缓存
            if tag_name in self._local_tags:
                self.stats['cache_hits'] += 1
                self.stats['get_operations'] += 1
                return self._local_tags[tag_name]

            # 从索引管理器获取
            owner_id = self.get_owner_id()
            all_tags = self.index_manager.get_object_tags(owner_id)

            if tag_name in all_tags:
                value = all_tags[tag_name]
                # 更新本地缓存
                self._local_tags[tag_name] = value
                self.stats['get_operations'] += 1
                return value

            # 从数据库加载
            value = self.load_tag_from_db(tag_name, default)
            if value != default:
                self._local_tags[tag_name] = value
                # 更新索引
                self.index_manager.add_tag(owner_id, tag_name, value, self.category)

            self.stats['get_operations'] += 1
            return value

        except Exception as e:
            logger.log_err(f"获取标签失败: {e}")
            return default

    def has(self, tag_name: str) -> bool:
        """检查是否有指定标签"""
        return self.get(tag_name, None) is not None

    def remove(self, tag_name: str):
        """移除标签"""
        try:
            owner_id = self.get_owner_id()

            # 从本地缓存移除
            if tag_name in self._local_tags:
                del self._local_tags[tag_name]

            # 从索引移除
            self.index_manager.remove_tag(owner_id, tag_name, self.category)

            # 从数据库移除
            self.remove_tag_from_db(tag_name)

            # 从脏标签集合移除
            self._dirty_tags.discard(tag_name)

        except Exception as e:
            logger.log_err(f"移除标签失败: {e}")

    def all(self) -> Dict[str, Any]:
        """获取所有标签"""
        try:
            owner_id = self.get_owner_id()

            # 合并本地缓存和索引数据
            all_tags = self.index_manager.get_object_tags(owner_id).copy()
            all_tags.update(self._local_tags)

            return all_tags

        except Exception as e:
            logger.log_err(f"获取所有标签失败: {e}")
            return {}

    def clear(self):
        """清除所有标签"""
        try:
            owner_id = self.get_owner_id()

            # 获取所有标签名
            all_tags = self.all()

            # 逐个移除
            for tag_name in all_tags:
                self.index_manager.remove_tag(owner_id, tag_name, self.category)

            # 清除本地缓存
            self._local_tags.clear()
            self._dirty_tags.clear()

            # 清除数据库数据
            self.clear_tags_from_db()

        except Exception as e:
            logger.log_err(f"清除标签失败: {e}")

    def persist_tag(self, tag_name: str, tag_value: Any):
        """持久化标签到数据库"""
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'attributes'):
            # 使用Evennia的attributes系统持久化
            attr_key = f"tagprop_{self.category}_{tag_name}"
            owner.attributes.add(attr_key, tag_value, category="tagproperty")

    def load_tag_from_db(self, tag_name: str, default: Any = None) -> Any:
        """从数据库加载标签"""
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'attributes'):
            attr_key = f"tagprop_{self.category}_{tag_name}"
            return owner.attributes.get(attr_key, default, category="tagproperty")
        return default

    def remove_tag_from_db(self, tag_name: str):
        """从数据库移除标签"""
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'attributes'):
            attr_key = f"tagprop_{self.category}_{tag_name}"
            owner.attributes.remove(attr_key, category="tagproperty")

    def clear_tags_from_db(self):
        """清除数据库中的所有标签"""
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'attributes'):
            # 获取所有tagproperty分类的属性
            all_attrs = owner.attributes.all(category="tagproperty")
            prefix = f"tagprop_{self.category}_"

            for attr in all_attrs:
                if attr.key.startswith(prefix):
                    owner.attributes.remove(attr.key, category="tagproperty")

    def sync_to_db(self):
        """同步脏数据到数据库"""
        for tag_name in self._dirty_tags:
            if tag_name in self._local_tags:
                self.persist_tag(tag_name, self._local_tags[tag_name])

        self._dirty_tags.clear()

    def get_stats(self) -> dict:
        """获取性能统计"""
        total_ops = self.stats['get_operations'] + self.stats['set_operations']
        cache_hit_rate = (self.stats['cache_hits'] / max(total_ops, 1)) * 100

        return {
            'get_operations': self.stats['get_operations'],
            'set_operations': self.stats['set_operations'],
            'query_operations': self.stats['query_operations'],
            'cache_hits': self.stats['cache_hits'],
            'cache_hit_rate': f"{cache_hit_rate:.2f}%",
            'local_cache_size': len(self._local_tags),
            'dirty_tags_count': len(self._dirty_tags)
        }


class CultivationTagProperty(TagProperty):
    """修仙专用TagProperty扩展"""

    def __init__(self, owner):
        super().__init__(owner, category="cultivation")

        # 修仙境界层次结构
        self.realm_hierarchy = {
            "练气期": {"max_level": 12, "tier": 1},
            "筑基期": {"max_level": 9, "tier": 2},
            "金丹期": {"max_level": 9, "tier": 3},
            "元婴期": {"max_level": 9, "tier": 4},
            "化神期": {"max_level": 9, "tier": 5},
            "炼虚期": {"max_level": 9, "tier": 6},
            "合体期": {"max_level": 9, "tier": 7},
            "大乘期": {"max_level": 9, "tier": 8},
            "渡劫期": {"max_level": 9, "tier": 9}
        }

    def set_realm(self, realm: str, level: int):
        """设置修仙境界"""
        if realm not in self.realm_hierarchy:
            logger.log_err(f"未知境界: {realm}")
            return False

        max_level = self.realm_hierarchy[realm]["max_level"]
        if level < 1 or level > max_level:
            logger.log_err(f"境界层次超出范围: {realm} {level}")
            return False

        # 设置境界相关标签
        self.set("realm", realm)
        self.set("level", level)
        self.set("realm_tier", self.realm_hierarchy[realm]["tier"])
        self.set("realm_power", self.calculate_realm_power(realm, level))

        return True

    def get_realm(self) -> Tuple[str, int]:
        """获取当前境界"""
        realm = self.get("realm", "练气期")
        level = self.get("level", 1)
        return realm, level

    def calculate_realm_power(self, realm: str, level: int) -> int:
        """计算境界战力值"""
        tier = self.realm_hierarchy[realm]["tier"]
        base_power = tier * 1000
        level_bonus = level * 100
        return base_power + level_bonus

    def can_breakthrough(self) -> bool:
        """检查是否可以突破"""
        realm, level = self.get_realm()
        max_level = self.realm_hierarchy[realm]["max_level"]

        cultivation_points = self.get("cultivation_points", 0)
        required_points = self.get_breakthrough_requirement(realm, level)

        return cultivation_points >= required_points

    def get_breakthrough_requirement(self, realm: str, level: int) -> int:
        """获取突破所需修为点数"""
        tier = self.realm_hierarchy[realm]["tier"]
        base_requirement = 1000 * (tier ** 2)
        level_multiplier = level * 1.5

        return int(base_requirement * level_multiplier)

    def advance_cultivation(self, points: int):
        """增加修为点数"""
        current_points = self.get("cultivation_points", 0)
        new_points = current_points + points
        self.set("cultivation_points", new_points)

        # 检查是否可以自动突破
        if self.can_breakthrough():
            self.set("can_breakthrough", True)

    def get_cultivation_progress(self) -> dict:
        """获取修仙进度"""
        realm, level = self.get_realm()
        cultivation_points = self.get("cultivation_points", 0)
        required_points = self.get_breakthrough_requirement(realm, level)

        progress_percentage = min((cultivation_points / required_points) * 100, 100)

        return {
            "realm": realm,
            "level": level,
            "cultivation_points": cultivation_points,
            "required_points": required_points,
            "progress_percentage": progress_percentage,
            "can_breakthrough": self.can_breakthrough()
        }


class AIDirectorQueryInterface:
    """AI导演查询接口 - 为AI系统提供语义化查询"""

    def __init__(self, index_manager: TagIndexManager = None):
        self.index_manager = index_manager or TagIndexManager()
        self.query = TagQuery(self.index_manager)

    def find_characters_by_realm(self, realm: str) -> Set[str]:
        """查找指定境界的角色"""
        return self.query.find_by_value(realm)

    def find_characters_in_realm_range(self, min_tier: int, max_tier: int) -> Set[str]:
        """查找境界等级范围内的角色"""
        return self.query.find_by_range("realm_tier", min_tier, max_tier)

    def find_powerful_characters(self, min_power: int) -> Set[str]:
        """查找战力超过指定值的角色"""
        return self.query.find_by_range("realm_power", min_power, float('inf'))

    def find_characters_ready_for_breakthrough(self) -> Set[str]:
        """查找可以突破的角色"""
        return self.query.find_by_value(True)  # can_breakthrough = True

    def find_characters_by_location_and_realm(self, location: str, min_tier: int) -> Set[str]:
        """查找特定地点且境界达到要求的角色"""
        location_chars = self.query.find_by_value(location)
        realm_chars = self.query.find_by_range("realm_tier", min_tier, float('inf'))

        return location_chars.intersection(realm_chars)

    def get_narrative_context(self, character_id: str) -> dict:
        """获取角色的叙事上下文"""
        tags = self.index_manager.get_object_tags(character_id)

        context = {
            'character_id': character_id,
            'realm': tags.get('realm', '练气期'),
            'level': tags.get('level', 1),
            'realm_tier': tags.get('realm_tier', 1),
            'realm_power': tags.get('realm_power', 1100),
            'cultivation_points': tags.get('cultivation_points', 0),
            'can_breakthrough': tags.get('can_breakthrough', False),
            'location': tags.get('location', '未知'),
            'sect': tags.get('sect', '散修'),
            'karma_balance': tags.get('karma_balance', 0)
        }

        return context

    def suggest_story_events(self, character_id: str) -> List[dict]:
        """为角色推荐故事事件"""
        context = self.get_narrative_context(character_id)
        suggestions = []

        # 基于境界推荐事件
        if context['can_breakthrough']:
            suggestions.append({
                'type': 'breakthrough_opportunity',
                'description': f"境界突破机会 - {context['realm']}第{context['level']}层",
                'priority': 'high'
            })

        # 基于战力推荐事件
        if context['realm_power'] > 5000:
            suggestions.append({
                'type': 'sect_invitation',
                'description': '强大宗门邀请加入',
                'priority': 'medium'
            })

        # 基于因果推荐事件
        karma = context['karma_balance']
        if karma > 1000:
            suggestions.append({
                'type': 'fortune_event',
                'description': '善有善报，获得机缘',
                'priority': 'medium'
            })
        elif karma < -1000:
            suggestions.append({
                'type': 'calamity_event',
                'description': '恶有恶报，遭遇劫难',
                'priority': 'high'
            })

        return suggestions


class TagPropertyMixin:
    """为Evennia对象提供TagProperty功能的Mixin"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._tagproperty_instances = {}

    @property
    def tags(self) -> TagProperty:
        """获取默认TagProperty实例"""
        if 'default' not in self._tagproperty_instances:
            self._tagproperty_instances['default'] = TagProperty(self, 'default')
        return self._tagproperty_instances['default']

    @property
    def cultivation(self) -> CultivationTagProperty:
        """获取修仙TagProperty实例"""
        if 'cultivation' not in self._tagproperty_instances:
            self._tagproperty_instances['cultivation'] = CultivationTagProperty(self)
        return self._tagproperty_instances['cultivation']

    def get_tagproperty(self, category: str) -> TagProperty:
        """获取指定分类的TagProperty实例"""
        if category not in self._tagproperty_instances:
            if category == 'cultivation':
                self._tagproperty_instances[category] = CultivationTagProperty(self)
            else:
                self._tagproperty_instances[category] = TagProperty(self, category)

        return self._tagproperty_instances[category]

    def sync_all_tagproperties(self):
        """同步所有TagProperty到数据库"""
        for tagprop in self._tagproperty_instances.values():
            tagprop.sync_to_db()

    def get_tagproperty_stats(self) -> dict:
        """获取所有TagProperty的性能统计"""
        stats = {}
        for category, tagprop in self._tagproperty_instances.items():
            stats[category] = tagprop.get_stats()

        return stats

    def has(self, tag_name: str) -> bool:
        """检查是否有指定标签"""
        return self.get(tag_name, None) is not None

    def remove(self, tag_name: str):
        """移除标签"""
        try:
            owner_id = self.get_owner_id()

            # 从本地缓存移除
            if tag_name in self._local_tags:
                del self._local_tags[tag_name]

            # 从索引移除
            self.index_manager.remove_tag(owner_id, tag_name, self.category)

            # 从数据库移除
            self.remove_tag_from_db(tag_name)

            # 从脏标签集合移除
            self._dirty_tags.discard(tag_name)

        except Exception as e:
            logger.log_err(f"移除标签失败: {e}")

    def all(self) -> Dict[str, Any]:
        """获取所有标签"""
        try:
            owner_id = self.get_owner_id()

            # 合并本地缓存和索引数据
            all_tags = self.index_manager.get_object_tags(owner_id).copy()
            all_tags.update(self._local_tags)

            return all_tags

        except Exception as e:
            logger.log_err(f"获取所有标签失败: {e}")
            return {}

    def clear(self):
        """清除所有标签"""
        try:
            owner_id = self.get_owner_id()

            # 获取所有标签名
            all_tags = self.all()

            # 逐个移除
            for tag_name in all_tags:
                self.index_manager.remove_tag(owner_id, tag_name, self.category)

            # 清除本地缓存
            self._local_tags.clear()
            self._dirty_tags.clear()

            # 清除数据库数据
            self.clear_tags_from_db()

        except Exception as e:
            logger.log_err(f"清除标签失败: {e}")

    def persist_tag(self, tag_name: str, tag_value: Any):
        """持久化标签到数据库"""
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'attributes'):
            # 使用Evennia的attributes系统持久化
            attr_key = f"tagprop_{self.category}_{tag_name}"
            owner.attributes.add(attr_key, tag_value, category="tagproperty")

    def load_tag_from_db(self, tag_name: str, default: Any = None) -> Any:
        """从数据库加载标签"""
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'attributes'):
            attr_key = f"tagprop_{self.category}_{tag_name}"
            return owner.attributes.get(attr_key, default, category="tagproperty")
        return default

    def remove_tag_from_db(self, tag_name: str):
        """从数据库移除标签"""
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'attributes'):
            attr_key = f"tagprop_{self.category}_{tag_name}"
            owner.attributes.remove(attr_key, category="tagproperty")

    def clear_tags_from_db(self):
        """清除数据库中的所有标签"""
        owner = self.owner() if self.owner else None
        if owner and hasattr(owner, 'attributes'):
            # 获取所有tagproperty分类的属性
            all_attrs = owner.attributes.all(category="tagproperty")
            prefix = f"tagprop_{self.category}_"

            for attr in all_attrs:
                if attr.key.startswith(prefix):
                    owner.attributes.remove(attr.key, category="tagproperty")

    def sync_to_db(self):
        """同步脏数据到数据库"""
        for tag_name in self._dirty_tags:
            if tag_name in self._local_tags:
                self.persist_tag(tag_name, self._local_tags[tag_name])

        self._dirty_tags.clear()

    def get_stats(self) -> dict:
        """获取性能统计"""
        total_ops = self.stats['get_operations'] + self.stats['set_operations']
        cache_hit_rate = (self.stats['cache_hits'] / max(total_ops, 1)) * 100

        return {
            'get_operations': self.stats['get_operations'],
            'set_operations': self.stats['set_operations'],
            'query_operations': self.stats['query_operations'],
            'cache_hits': self.stats['cache_hits'],
            'cache_hit_rate': f"{cache_hit_rate:.2f}%",
            'local_cache_size': len(self._local_tags),
            'dirty_tags_count': len(self._dirty_tags)
        }


class CultivationTagProperty(TagProperty):
    """修仙专用TagProperty扩展"""

    def __init__(self, owner):
        super().__init__(owner, category="cultivation")

        # 修仙境界层次结构
        self.realm_hierarchy = {
            "练气期": {"max_level": 12, "tier": 1},
            "筑基期": {"max_level": 9, "tier": 2},
            "金丹期": {"max_level": 9, "tier": 3},
            "元婴期": {"max_level": 9, "tier": 4},
            "化神期": {"max_level": 9, "tier": 5},
            "炼虚期": {"max_level": 9, "tier": 6},
            "合体期": {"max_level": 9, "tier": 7},
            "大乘期": {"max_level": 9, "tier": 8},
            "渡劫期": {"max_level": 9, "tier": 9}
        }

    def set_realm(self, realm: str, level: int):
        """设置修仙境界"""
        if realm not in self.realm_hierarchy:
            logger.log_err(f"未知境界: {realm}")
            return False

        max_level = self.realm_hierarchy[realm]["max_level"]
        if level < 1 or level > max_level:
            logger.log_err(f"境界层次超出范围: {realm} {level}")
            return False

        # 设置境界相关标签
        self.set("realm", realm)
        self.set("level", level)
        self.set("realm_tier", self.realm_hierarchy[realm]["tier"])
        self.set("realm_power", self.calculate_realm_power(realm, level))

        return True

    def get_realm(self) -> Tuple[str, int]:
        """获取当前境界"""
        realm = self.get("realm", "练气期")
        level = self.get("level", 1)
        return realm, level

    def calculate_realm_power(self, realm: str, level: int) -> int:
        """计算境界战力值"""
        tier = self.realm_hierarchy[realm]["tier"]
        base_power = tier * 1000
        level_bonus = level * 100
        return base_power + level_bonus

    def can_breakthrough(self) -> bool:
        """检查是否可以突破"""
        realm, level = self.get_realm()
        max_level = self.realm_hierarchy[realm]["max_level"]

        cultivation_points = self.get("cultivation_points", 0)
        required_points = self.get_breakthrough_requirement(realm, level)

        return cultivation_points >= required_points

    def get_breakthrough_requirement(self, realm: str, level: int) -> int:
        """获取突破所需修为点数"""
        tier = self.realm_hierarchy[realm]["tier"]
        base_requirement = 1000 * (tier ** 2)
        level_multiplier = level * 1.5

        return int(base_requirement * level_multiplier)

    def advance_cultivation(self, points: int):
        """增加修为点数"""
        current_points = self.get("cultivation_points", 0)
        new_points = current_points + points
        self.set("cultivation_points", new_points)

        # 检查是否可以自动突破
        if self.can_breakthrough():
            self.set("can_breakthrough", True)

    def get_cultivation_progress(self) -> dict:
        """获取修仙进度"""
        realm, level = self.get_realm()
        cultivation_points = self.get("cultivation_points", 0)
        required_points = self.get_breakthrough_requirement(realm, level)

        progress_percentage = min((cultivation_points / required_points) * 100, 100)

        return {
            "realm": realm,
            "level": level,
            "cultivation_points": cultivation_points,
            "required_points": required_points,
            "progress_percentage": progress_percentage,
            "can_breakthrough": self.can_breakthrough()
        }

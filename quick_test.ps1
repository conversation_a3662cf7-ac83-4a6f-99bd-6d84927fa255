# 快速测试脚本 - Windows环境
Write-Host "🎯 仙侠MUD快速状态检查" -ForegroundColor Green

# 切换到项目目录
Set-Location "xiuxian_mud_new"

# 1. 检查Evennia版本
Write-Host "`n📋 检查Evennia版本..." -ForegroundColor Yellow
& "..\xiuxian_venv\Scripts\python.exe" -c "import evennia; print('Evennia版本:', evennia.__version__)"

# 2. 检查服务器状态
Write-Host "`n📋 检查服务器状态..." -ForegroundColor Yellow
& "..\xiuxian_venv\Scripts\python.exe" -m evennia status

# 3. 检查数据库连接
Write-Host "`n📋 检查数据库连接..." -ForegroundColor Yellow
& "..\xiuxian_venv\Scripts\python.exe" -c "
import psycopg2
try:
    conn = psycopg2.connect(host='localhost', database='xiuxian_mud', user='postgres', password='zy123good')
    print('✅ PostgreSQL连接成功')
    conn.close()
except Exception as e:
    print('❌ PostgreSQL连接失败:', e)
"

# 4. 检查Web界面
Write-Host "`n📋 检查Web界面..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:4001" -TimeoutSec 5
    Write-Host "✅ Web界面响应正常 (状态码: $($response.StatusCode))"
} catch {
    Write-Host "❌ Web界面无法访问: $($_.Exception.Message)"
}

# 5. 运行项目状态检查
Write-Host "`n📋 运行详细项目检查..." -ForegroundColor Yellow
& "..\xiuxian_venv\Scripts\python.exe" simple_test.py

Write-Host "`n🎯 快速检查完成!" -ForegroundColor Green
Set-Location ".."

# 修仙MUD核心系统模块
# Day 3-4 实现：事件驱动系统、TagProperty高性能查询、Handler生态组件化框架

from .event_system import *
from .tagproperty_system import *
from .handler_system import *

__all__ = [
    # 事件系统
    'BaseEvent', 'XianxiaEventBus', 'BaseEventHandler', 'AIDirectorEventHandler',
    'CultivationBreakthroughEvent', 'CultivationProgressEvent', 'CelestialAnomalyEvent',
    'SkillCastEvent', 'CombatStateEvent', 'SectConflictEvent', 'MasterDiscipleEvent',
    'SpiritualEnergyEvent', 'EventFilter',
    
    # TagProperty系统
    'TagProperty', 'TagIndexManager', 'TagQuery', 'CultivationTagProperty',
    'AIDirectorQueryInterface', 'TagPropertyMixin',
    
    # Handler系统
    'lazy_property', 'BaseHandler', 'HandlerRegistry', 'HandlerMemoryManager',
    'HandlerMixin', 'handler_method'
]

name: Python

on:
  push:
    branches:
      - main
  pull_request:
  release:
    types: [published]

jobs:
  detect-packages:
    runs-on: ubuntu-latest
    outputs:
      packages: ${{ steps.find-packages.outputs.packages }}
    steps:
      - uses: actions/checkout@v4

      - name: Find Python packages
        id: find-packages
        working-directory: src
        run: |
          PACKAGES=$(find . -name pyproject.toml -exec dirname {} \; | sed 's/^\.\///' | jq -R -s -c 'split("\n")[:-1]')
          echo "packages=$PACKAGES" >> $GITHUB_OUTPUT

  test:
    needs: [detect-packages]
    strategy:
      matrix:
        package: ${{ fromJson(needs.detect-packages.outputs.packages) }}
    name: Test ${{ matrix.package }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version-file: "src/${{ matrix.package }}/.python-version"

      - name: Install dependencies
        working-directory: src/${{ matrix.package }}
        run: uv sync --frozen --all-extras --dev

      - name: Check if tests exist
        id: check-tests
        working-directory: src/${{ matrix.package }}
        run: |
          if [ -d "tests" ] || [ -d "test" ] || grep -q "pytest" pyproject.toml; then
            echo "has-tests=true" >> $GITHUB_OUTPUT
          else
            echo "has-tests=false" >> $GITHUB_OUTPUT
          fi

      - name: Run tests
        if: steps.check-tests.outputs.has-tests == 'true'
        working-directory: src/${{ matrix.package }}
        run: uv run pytest

  build:
    needs: [detect-packages, test]
    strategy:
      matrix:
        package: ${{ fromJson(needs.detect-packages.outputs.packages) }}
    name: Build ${{ matrix.package }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version-file: "src/${{ matrix.package }}/.python-version"

      - name: Install dependencies
        working-directory: src/${{ matrix.package }}
        run: uv sync --frozen --all-extras --dev

      - name: Run pyright
        working-directory: src/${{ matrix.package }}
        run: uv run --frozen pyright

      - name: Build package
        working-directory: src/${{ matrix.package }}
        run: uv build

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist-${{ matrix.package }}
          path: src/${{ matrix.package }}/dist/

  publish:
    runs-on: ubuntu-latest
    needs: [build, detect-packages]
    if: github.event_name == 'release'

    strategy:
      matrix:
        package: ${{ fromJson(needs.detect-packages.outputs.packages) }}
    name: Publish ${{ matrix.package }}

    environment: release
    permissions:
      id-token: write # Required for trusted publishing

    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist-${{ matrix.package }}
          path: dist/

      - name: Publish package to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1

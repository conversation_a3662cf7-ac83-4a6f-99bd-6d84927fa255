#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Day3-4核心功能实现测试
测试事件驱动系统、TagProperty、AI导演接口的实现状态
"""

import os
import sys
import time

# 确保在正确的目录
if not os.path.exists('server'):
    print("❌ 请在xiuxian_mud_new目录下运行此脚本")
    sys.exit(1)

import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
django.setup()

from evennia import ObjectDB, ScriptDB
from evennia.utils import create
from evennia.scripts.scripts import DefaultScript

def test_basic_evennia_functionality():
    """测试基础Evennia功能"""
    print("🔍 测试基础Evennia功能...")
    
    try:
        # 测试对象创建
        test_obj = create.create_object('typeclasses.objects.Object', key='测试对象')
        print(f"✅ 对象创建成功: {test_obj}")
        
        # 测试属性系统
        test_obj.attributes.add("test_attr", "测试值")
        value = test_obj.attributes.get("test_attr")
        print(f"✅ 属性系统正常: {value}")
        
        # 测试标签系统
        test_obj.tags.add("测试标签", category="test")
        has_tag = test_obj.tags.has("测试标签", category="test")
        print(f"✅ 标签系统正常: {has_tag}")
        
        # 清理
        test_obj.delete()
        print("✅ 对象清理完成")
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        return False
    
    return True

def test_event_system_implementation():
    """测试事件系统实现状态"""
    print("\n🔍 检查事件系统实现状态...")
    
    # 检查是否有事件系统相关的脚本
    event_scripts = ScriptDB.objects.filter(db_key__icontains="event")
    print(f"📊 发现事件相关脚本: {len(event_scripts)}个")
    
    for script in event_scripts:
        print(f"  - {script.key}: {script.desc}")
    
    # 检查是否有XianxiaEventBus
    try:
        from typeclasses.scripts import XianxiaEventBus
        print("✅ 发现XianxiaEventBus类定义")
        return True
    except ImportError:
        print("❌ 未发现XianxiaEventBus实现")
        print("💡 这是正常的，因为Day3-4的事件系统尚未实现")
        return False

def test_tagproperty_implementation():
    """测试TagProperty实现状态"""
    print("\n🔍 检查TagProperty实现状态...")
    
    try:
        # 检查角色类是否有TagProperty支持
        from typeclasses.characters import Character
        
        # 创建测试角色
        test_char = create.create_object(Character, key="测试角色")
        
        # 检查是否有tags属性
        if hasattr(test_char, 'tags'):
            print("✅ 角色类有tags属性")
            
            # 测试基础标签功能
            test_char.tags.add("境界", "炼气期", category="cultivation")
            realm = test_char.tags.get("境界", category="cultivation")
            print(f"✅ 基础标签功能正常: {realm}")
            
        else:
            print("❌ 角色类缺少TagProperty支持")
        
        # 清理
        test_char.delete()
        return True
        
    except Exception as e:
        print(f"❌ TagProperty测试失败: {e}")
        return False

def test_ai_director_interface():
    """测试AI导演接口实现状态"""
    print("\n🔍 检查AI导演接口实现状态...")
    
    try:
        # 检查AI配置
        from django.conf import settings
        ai_settings = getattr(settings, 'AI_SETTINGS', None)
        
        if ai_settings:
            print(f"✅ 发现AI配置: {ai_settings}")
        else:
            print("❌ 未发现AI配置")
        
        # 检查是否有AI导演相关的脚本
        ai_scripts = ScriptDB.objects.filter(db_key__icontains="ai")
        print(f"📊 发现AI相关脚本: {len(ai_scripts)}个")
        
        return True
        
    except Exception as e:
        print(f"❌ AI导演接口测试失败: {e}")
        return False

def test_performance_baseline():
    """测试性能基准"""
    print("\n🔍 性能基准测试...")
    
    try:
        # 测试对象查询性能
        start_time = time.time()
        objects = ObjectDB.objects.all()[:100]
        query_time = time.time() - start_time
        print(f"📊 查询100个对象耗时: {query_time:.4f}秒")
        
        # 测试标签查询性能
        start_time = time.time()
        tagged_objects = ObjectDB.objects.filter(db_tags__db_key="测试标签")[:10]
        tag_query_time = time.time() - start_time
        print(f"📊 标签查询耗时: {tag_query_time:.4f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 Day3-4核心功能实现状态检查")
    print("=" * 50)
    
    results = []
    
    # 运行各项测试
    results.append(("基础功能", test_basic_evennia_functionality()))
    results.append(("事件系统", test_event_system_implementation()))
    results.append(("TagProperty", test_tagproperty_implementation()))
    results.append(("AI导演接口", test_ai_director_interface()))
    results.append(("性能基准", test_performance_baseline()))
    
    # 输出测试结果
    print("\n📊 测试结果汇总:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体通过率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    # 给出建议
    if passed < len(results):
        print("\n💡 建议:")
        print("- Day3-4的核心功能尚未完全实现")
        print("- 需要实现事件驱动总线系统")
        print("- 需要实现TagProperty高性能查询系统")
        print("- 需要完善AI导演接口")

if __name__ == "__main__":
    main()

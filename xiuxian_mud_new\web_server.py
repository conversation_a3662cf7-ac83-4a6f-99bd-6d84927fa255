#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修仙MUD Web服务器 - 用于Playwright测试
提供Handler功能的HTTP接口
"""

import sys
import os
import json
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入Handler系统
from systems.handlers.cultivation_handler import CultivationHandler
from systems.handlers.combat_skill_handler import CombatSkillHandler
from systems.handlers.alchemy_handler import AlchemyHandler
from systems.handlers.karma_handler import KarmaHandler
from systems.handlers.ai_director_handler import AIDirectorHandler

app = Flask(__name__)
CORS(app)

# 模拟角色类
class MockCharacter:
    def __init__(self):
        self.name = "测试修仙者"
        self.key = "test_character"
        self.id = 1

# 全局Handler实例
handlers = {}
character = None

def initialize_handlers():
    """初始化所有Handler"""
    global handlers, character
    
    character = MockCharacter()
    
    try:
        # 创建所有Handler
        handlers['cultivation'] = CultivationHandler(character)
        handlers['combat'] = CombatSkillHandler(character)
        handlers['alchemy'] = AlchemyHandler(character)
        handlers['karma'] = KarmaHandler(character)
        handlers['ai_director'] = AIDirectorHandler(character)
        
        # 初始化AI导演Handler
        handlers['ai_director'].initialize()
        
        print("✅ 所有Handler初始化成功")
        return True
    except Exception as e:
        print(f"❌ Handler初始化失败: {e}")
        return False

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修仙MUD Handler测试界面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .handler-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .handler-title { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .result { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .input-group { margin: 10px 0; }
        .input-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .input-group input, .input-group select { width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧙‍♂️ 修仙MUD Handler测试界面</h1>
        <div id="status" class="status success">系统已就绪，所有Handler加载完成</div>
        
        <!-- 修仙系统 -->
        <div class="handler-section">
            <h2 class="handler-title">🧘‍♂️ 修仙系统</h2>
            <button class="button" onclick="getCultivationRealm()">获取当前境界</button>
            <button class="button" onclick="getCultivationProgress()">获取修炼进度</button>
            <button class="button" onclick="startCultivation()">开始修炼</button>
            <button class="button" onclick="attemptBreakthrough()">尝试突破</button>
            <div id="cultivation-result" class="result"></div>
        </div>
        
        <!-- 战斗技能系统 -->
        <div class="handler-section">
            <h2 class="handler-title">⚔️ 战斗技能系统</h2>
            <button class="button" onclick="getAvailableSkills()">获取可用技能</button>
            <button class="button" onclick="getLearnedSkills()">获取已学技能</button>
            <div class="input-group">
                <label>技能名称:</label>
                <input type="text" id="skill-name" value="火球术" placeholder="输入技能名称">
                <button class="button" onclick="learnSkill()">学习技能</button>
            </div>
            <div id="combat-result" class="result"></div>
        </div>
        
        <!-- 炼丹系统 -->
        <div class="handler-section">
            <h2 class="handler-title">🧪 炼丹系统</h2>
            <button class="button" onclick="getAlchemyRecipes()">获取配方</button>
            <button class="button" onclick="getMaterials()">获取材料</button>
            <div class="input-group">
                <label>材料名称:</label>
                <input type="text" id="material-name" value="灵石" placeholder="输入材料名称">
                <label>数量:</label>
                <input type="number" id="material-amount" value="5" placeholder="输入数量">
                <button class="button" onclick="addMaterial()">添加材料</button>
            </div>
            <div id="alchemy-result" class="result"></div>
        </div>
        
        <!-- 因果系统 -->
        <div class="handler-section">
            <h2 class="handler-title">⚖️ 因果系统</h2>
            <button class="button" onclick="getKarmaStatus()">获取因果状态</button>
            <div class="input-group">
                <label>行为类型:</label>
                <select id="karma-type">
                    <option value="good">善行</option>
                    <option value="evil">恶行</option>
                </select>
                <label>数值:</label>
                <input type="number" id="karma-amount" value="10" placeholder="输入数值">
                <label>描述:</label>
                <input type="text" id="karma-desc" value="帮助他人" placeholder="输入描述">
                <button class="button" onclick="recordKarma()">记录因果</button>
            </div>
            <div id="karma-result" class="result"></div>
        </div>
        
        <!-- AI导演系统 -->
        <div class="handler-section">
            <h2 class="handler-title">🎭 AI导演系统</h2>
            <button class="button" onclick="getStoryStatus()">获取故事状态</button>
            <button class="button" onclick="getWorldState()">获取世界状态</button>
            <div class="input-group">
                <label>上下文:</label>
                <input type="text" id="context" value="角色开始修炼" placeholder="输入上下文">
                <button class="button" onclick="updateContext()">更新上下文</button>
            </div>
            <div id="ai-director-result" class="result"></div>
        </div>
    </div>

    <script>
        // 通用API调用函数
        async function callAPI(endpoint, data = null) {
            try {
                const options = {
                    method: data ? 'POST' : 'GET',
                    headers: { 'Content-Type': 'application/json' }
                };
                if (data) options.body = JSON.stringify(data);
                
                const response = await fetch(endpoint, options);
                const result = await response.json();
                return result;
            } catch (error) {
                return { error: error.message };
            }
        }
        
        // 显示结果
        function showResult(elementId, result) {
            document.getElementById(elementId).innerHTML = 
                '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
        }
        
        // 修仙系统API
        async function getCultivationRealm() {
            const result = await callAPI('/api/cultivation/realm');
            showResult('cultivation-result', result);
        }
        
        async function getCultivationProgress() {
            const result = await callAPI('/api/cultivation/progress');
            showResult('cultivation-result', result);
        }
        
        async function startCultivation() {
            const result = await callAPI('/api/cultivation/cultivate', {});
            showResult('cultivation-result', result);
        }
        
        async function attemptBreakthrough() {
            const result = await callAPI('/api/cultivation/breakthrough', {});
            showResult('cultivation-result', result);
        }
        
        // 战斗技能系统API
        async function getAvailableSkills() {
            const result = await callAPI('/api/combat/available-skills');
            showResult('combat-result', result);
        }
        
        async function getLearnedSkills() {
            const result = await callAPI('/api/combat/learned-skills');
            showResult('combat-result', result);
        }
        
        async function learnSkill() {
            const skillName = document.getElementById('skill-name').value;
            const result = await callAPI('/api/combat/learn-skill', { skill_name: skillName });
            showResult('combat-result', result);
        }
        
        // 炼丹系统API
        async function getAlchemyRecipes() {
            const result = await callAPI('/api/alchemy/recipes');
            showResult('alchemy-result', result);
        }
        
        async function getMaterials() {
            const result = await callAPI('/api/alchemy/materials');
            showResult('alchemy-result', result);
        }
        
        async function addMaterial() {
            const name = document.getElementById('material-name').value;
            const amount = parseInt(document.getElementById('material-amount').value);
            const result = await callAPI('/api/alchemy/add-material', { name, amount });
            showResult('alchemy-result', result);
        }
        
        // 因果系统API
        async function getKarmaStatus() {
            const result = await callAPI('/api/karma/status');
            showResult('karma-result', result);
        }
        
        async function recordKarma() {
            const karmaType = document.getElementById('karma-type').value;
            const amount = parseInt(document.getElementById('karma-amount').value);
            const description = document.getElementById('karma-desc').value;
            const result = await callAPI('/api/karma/record', { 
                karma_type: karmaType, amount, description 
            });
            showResult('karma-result', result);
        }
        
        // AI导演系统API
        async function getStoryStatus() {
            const result = await callAPI('/api/ai-director/story-status');
            showResult('ai-director-result', result);
        }
        
        async function getWorldState() {
            const result = await callAPI('/api/ai-director/world-state');
            showResult('ai-director-result', result);
        }
        
        async function updateContext() {
            const context = document.getElementById('context').value;
            const result = await callAPI('/api/ai-director/update-context', { context });
            showResult('ai-director-result', result);
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

# 修仙系统API
@app.route('/api/cultivation/realm', methods=['GET'])
def get_cultivation_realm():
    try:
        result = handlers['cultivation'].get_current_realm()
        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/cultivation/progress', methods=['GET'])
def get_cultivation_progress():
    try:
        result = handlers['cultivation'].get_cultivation_progress()
        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/cultivation/cultivate', methods=['POST'])
def start_cultivation():
    try:
        result = handlers['cultivation'].start_cultivation()
        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/cultivation/breakthrough', methods=['POST'])
def attempt_breakthrough():
    try:
        result = handlers['cultivation'].attempt_breakthrough()
        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

if __name__ == '__main__':
    print("🚀 启动修仙MUD Web服务器...")
    
    if initialize_handlers():
        print("🌐 Web服务器启动成功: http://localhost:5000")
        app.run(host='0.0.0.0', port=5000, debug=True)
    else:
        print("❌ Handler初始化失败，无法启动服务器")

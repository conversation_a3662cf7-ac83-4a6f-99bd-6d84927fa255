# 🎯 仙侠MUD游戏 Windows环境测试指南

## 📊 当前项目状态 (Day3-4)

### ✅ 已完成的基础设施 (Day1-2)
- **开发环境**: Python 3.11 + Evennia 4.5.0 ✅
- **数据库**: PostgreSQL配置完成 ✅  
- **缓存**: Redis配置完成 ✅
- **项目结构**: xiuxian_mud_new项目创建 ✅
- **服务器**: Evennia服务器正常运行 ✅

### ❌ 待实现的核心功能 (Day3-4)
- **事件驱动总线系统**: 未实现
- **TagProperty高性能查询**: 未实现  
- **AI导演接口**: 基础配置已设置，核心功能未实现
- **Handler组件化生态**: 未实现

## 🧪 Windows环境测试方案

### 1. 基础环境验证

#### 1.1 虚拟环境测试
```powershell
# 在项目根目录 d:\project\evennia 执行
cd xiuxian_mud_new
..\xiuxian_venv\Scripts\python.exe -c "import evennia; print('Evennia版本:', evennia.__version__)"
```

#### 1.2 数据库连接测试
```powershell
..\xiuxian_venv\Scripts\python.exe -c "
import psycopg2
try:
    conn = psycopg2.connect(host='localhost', database='xiuxian_mud', user='postgres', password='zy123good')
    print('✅ PostgreSQL连接成功')
    conn.close()
except Exception as e:
    print('❌ PostgreSQL连接失败:', e)
"
```

#### 1.3 Redis连接测试
```powershell
..\xiuxian_venv\Scripts\python.exe -c "
import redis
try:
    r = redis.Redis(host='127.0.0.1', port=6379, db=1)
    r.ping()
    print('✅ Redis连接成功')
except Exception as e:
    print('❌ Redis连接失败:', e)
"
```

### 2. Evennia服务器测试

#### 2.1 服务器状态检查
```powershell
..\xiuxian_venv\Scripts\python.exe -m evennia status
```

#### 2.2 服务器重启测试
```powershell
# 停止服务器
..\xiuxian_venv\Scripts\python.exe -m evennia stop

# 启动服务器
..\xiuxian_venv\Scripts\python.exe -m evennia start

# 检查状态
..\xiuxian_venv\Scripts\python.exe -m evennia status
```

#### 2.3 Web界面测试
- 打开浏览器访问: http://localhost:4001
- 使用管理员账户登录: admin / zy123good

### 3. 项目结构验证

#### 3.1 运行项目状态检查
```powershell
..\xiuxian_venv\Scripts\python.exe simple_test.py
```

#### 3.2 手动检查关键文件
```powershell
# 检查配置文件
type server\conf\secret_settings.py

# 检查角色类
type typeclasses\characters.py

# 检查设计文档
dir ..\*.md
```

### 4. Day3-4功能实现测试

#### 4.1 事件系统测试
目前状态: ❌ 未实现
```powershell
# 检查是否有事件相关代码
findstr /s /i "event" typeclasses\*.py
findstr /s /i "XianxiaEventBus" typeclasses\*.py
```

#### 4.2 TagProperty系统测试  
目前状态: ❌ 未实现
```powershell
# 检查是否有TagProperty相关代码
findstr /s /i "TagProperty" typeclasses\*.py
findstr /s /i "tags" typeclasses\characters.py
```

#### 4.3 AI导演接口测试
目前状态: ⚠️ 基础配置存在，核心功能未实现
```powershell
# 检查AI配置
findstr /i "AI_SETTINGS" server\conf\secret_settings.py
```

### 5. 性能基准测试

#### 5.1 基础查询性能
```powershell
..\xiuxian_venv\Scripts\python.exe -c "
import time
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
import django
django.setup()

from evennia import ObjectDB
start = time.time()
objects = list(ObjectDB.objects.all()[:10])
end = time.time()
print(f'查询10个对象耗时: {end-start:.4f}秒')
print(f'对象数量: {len(objects)}')
"
```

### 6. 网络连接测试

#### 6.1 Telnet连接测试
```powershell
# 使用Windows内置telnet (需要先启用telnet客户端功能)
telnet localhost 4000
```

#### 6.2 WebSocket连接测试
- 访问 http://localhost:4001/webclient
- 测试实时通信功能

## 🔧 常见问题解决

### 问题1: PowerShell执行策略限制
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 问题2: 中文编码问题
```powershell
# 设置PowerShell编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
```

### 问题3: 端口占用
```powershell
# 检查端口占用
netstat -ano | findstr :4000
netstat -ano | findstr :4001

# 结束占用进程 (替换PID)
taskkill /PID <PID> /F
```

### 问题4: 数据库连接失败
1. 确认PostgreSQL服务运行: `services.msc`
2. 检查防火墙设置
3. 验证用户权限

## 📋 测试检查清单

### Day1-2基础设施验证
- [ ] Python虚拟环境正常
- [ ] Evennia版本正确 (4.5.0)
- [ ] PostgreSQL连接成功
- [ ] Redis连接成功
- [ ] Evennia服务器启动成功
- [ ] Web界面可访问
- [ ] 管理员账户可登录

### Day3-4功能实现检查
- [ ] 事件驱动总线系统 (❌ 未实现)
- [ ] TagProperty查询系统 (❌ 未实现)
- [ ] AI导演接口 (⚠️ 部分实现)
- [ ] Handler组件生态 (❌ 未实现)

### 性能指标验证
- [ ] 基础查询响应时间 < 100ms
- [ ] 服务器启动时间 < 30秒
- [ ] Web界面加载时间 < 5秒
- [ ] 内存使用 < 500MB

## 🎯 下一步开发建议

基于测试结果，建议按以下顺序实现Day3-4功能:

1. **事件驱动总线系统** (优先级: 🔥 高)
   - 实现XianxiaEventBus核心类
   - 创建基础事件类型定义
   - 集成到角色和对象系统

2. **TagProperty查询系统** (优先级: 🔥 高)  
   - 扩展角色类支持TagProperty
   - 实现语义化标签体系
   - 性能优化和基准测试

3. **AI导演接口完善** (优先级: 🔥 高)
   - 实现AI决策引擎
   - 集成事件监听机制
   - 测试AI响应功能

4. **Handler组件生态** (优先级: 🔥 中)
   - 实现@lazy_property模式
   - 创建核心Handler组件
   - 内存优化验证

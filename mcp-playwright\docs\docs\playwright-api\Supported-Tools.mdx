---
sidebar_position: 1
---

import YouTubeVideoEmbed from '@site/src/components/HomepageFeatures/YouTubeVideoEmbed';


# 🛠️ Supported Tools

Playwright MCP for API automation has following key features
- Support of GET Request
- Support of POST Request
- Support of PATCH Request
- Support of PUT Request
- Support of DELETE Request


<YouTubeVideoEmbed videoId="BYYyoRxCcFE" />

---

:::warning Note
Still the library is not matured enough to support Oauth, Multi-form, Binary input or complex API requests. Please feel free to fork the repo and add the feature with a PR, will can build the library together!
:::

### Playwright_get
Perform a GET operation on any given API request.

- **Inputs:**
  - **`url`** *(string)*:  
    URL to perform the GET operation.

- **Response:**
  - **`statusCode`** *(string)*:  
    Status code of the API.

---

### Playwright_post
Perform a POST operation on any given API request.

- **Inputs:**
  - **`url`** *(string)*:  
    URL to perform the POST operation.  
  - **`value`** *(string)*:  
    Data to include in the body of the POST request.
  - **`token`** *(string, optional)*:  
    Bearer token for authorization. When provided, it will be sent as `Authorization: Bearer <token>` header.
  - **`headers`** *(object, optional)*:  
    Additional headers to include in the request. Note: Content-Type: application/json is set by default.

- **Response:**
  - **`statusCode`** *(string)*:  
    Status code of the API.  
  - **`responseData`** *(string)*:  
    Response data in JSON format.

---

### Playwright_put
Perform a PUT operation on any given API request.

- **Inputs:**
  - **`url`** *(string)*:  
    URL to perform the PUT operation.  
  - **`value`** *(string)*:  
    Data to include in the body of the PUT request.

- **Response:**
  - **`statusCode`** *(string)*:  
    Status code of the API.  
  - **`responseData`** *(string)*:  
    Response data in JSON format.

---

### Playwright_patch
Perform a PATCH operation on any given API request.

- **Inputs:**
  - **`url`** *(string)*:  
    URL to perform the PATCH operation.  
  - **`value`** *(string)*:  
    Data to include in the body of the PATCH request.

- **Response:**
  - **`statusCode`** *(string)*:  
    Status code of the API.  
  - **`responseData`** *(string)*:  
    Response data in JSON format.

---

### Playwright_delete
Perform a DELETE operation on any given API request.

- **Inputs:**
  - **`url`** *(string)*:  
    URL to perform the DELETE operation.

- **Response:**
  - **`statusCode`** *(string)*:  
    Status code of the API.

### Upon running the test Claude Desktop will run MCP Server to use above tools
![Playwright MCP Server](./img/playwright-api.png)
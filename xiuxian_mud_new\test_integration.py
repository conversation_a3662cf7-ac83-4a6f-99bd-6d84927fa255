#!/usr/bin/env python
"""
修仙MUD系统集成测试
测试Day3-4实现的所有系统组件
"""

import os
import sys
import django
from django.conf import settings

# 设置Django环境
if not settings.configured:
    # 添加项目路径
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    # 配置Django设置
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
    django.setup()

def test_system_imports():
    """测试系统导入"""
    print("=== 测试系统导入 ===")
    
    try:
        # 测试事件系统
        from systems.event_system import XianxiaEventBus, BaseEvent
        print("✓ 事件系统导入成功")
        
        # 测试TagProperty系统
        from systems.tagproperty_system import TagProperty, TagPropertyMixin
        print("✓ TagProperty系统导入成功")
        
        # 测试Handler系统
        from systems.handler_system import <PERSON>Handler, HandlerMixin, lazy_property
        print("✓ Handler系统导入成功")
        
        # 测试Handler实现
        from systems.handlers import (
            CultivationHandler, CombatSkillHandler, AlchemyHandler,
            KarmaHandler, AIDirectorHandler
        )
        print("✓ 所有Handler实现导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_handler_creation():
    """测试Handler创建"""
    print("\n=== 测试Handler创建 ===")
    
    try:
        from systems.handlers import CultivationHandler
        
        # 创建模拟对象
        class MockCharacter:
            def __init__(self):
                self.id = "test_char"
                self.key = "test_char"  # 添加key属性
                self.name = "测试角色"
                self.tags = MockTags()
                self.date_created = None
                
        class MockTags:
            def __init__(self):
                self._data = {}
            
            def set(self, key, value):
                self._data[key] = value
            
            def get(self, key, default=None):
                return self._data.get(key, default)
            
            def has(self, key):
                return key in self._data
        
        # 创建Handler
        char = MockCharacter()
        handler = CultivationHandler(char)
        
        print("✓ CultivationHandler创建成功")
        
        # 测试基础方法
        if hasattr(handler, 'get_current_realm'):
            print("✓ Handler方法可用")
        
        return True
        
    except Exception as e:
        print(f"✗ Handler创建失败: {e}")
        return False

def test_event_system():
    """测试事件系统"""
    print("\n=== 测试事件系统 ===")
    
    try:
        from systems.event_system import XianxiaEventBus, CultivationBreakthroughEvent
        
        # 创建事件总线实例（因为在测试环境中）
        event_bus = XianxiaEventBus()
        print("✓ 事件总线创建成功")
        
        # 创建测试事件
        event = CultivationBreakthroughEvent(
            event_type="cultivation_breakthrough",
            source_id="test_char",
            data={
                "old_realm": "练气期",
                "new_realm": "筑基期",
                "old_level": 9,
                "new_level": 1
            }
        )
        print("✓ 事件创建成功")
        
        # 发送事件
        event_bus.emit_event(event)
        print("✓ 事件发送成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 事件系统测试失败: {e}")
        return False

def test_tagproperty_system():
    """测试TagProperty系统"""
    print("\n=== 测试TagProperty系统 ===")
    
    try:
        from systems.tagproperty_system import TagProperty
        
        # 创建模拟对象
        class MockObject:
            def __init__(self):
                self.id = "test_obj"
                self._tags = {}
            
            def tags_get(self, key, default=None):
                return self._tags.get(key, default)
            
            def tags_set(self, key, value):
                self._tags[key] = value
                return True
        
        # 创建TagProperty
        obj = MockObject()
        tag_prop = TagProperty(obj, "test_category")
        
        print("✓ TagProperty创建成功")
        
        # 测试设置和获取
        tag_prop.set("test_key", "test_value")
        value = tag_prop.get("test_key")
        
        if value == "test_value":
            print("✓ TagProperty读写功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ TagProperty系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("修仙MUD系统集成测试开始")
    print("=" * 50)
    
    tests = [
        test_system_imports,
        test_handler_creation,
        test_event_system,
        test_tagproperty_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有系统集成测试通过！")
        return True
    else:
        print("❌ 部分测试失败，需要检查系统配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python
"""
修仙MUD Handler系统最终测试
使用模拟对象测试Handler功能
"""

import os
import sys
import django
from django.conf import settings

# 设置Django环境
if not settings.configured:
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
    django.setup()

class MockCharacter:
    """模拟Character对象"""
    def __init__(self):
        self.key = "测试修仙者"
        self.id = "test_char_001"
        self.name = "测试修仙者"
        self.tags = MockTags()
        self.date_created = None
        self._handlers = {}
        
    def msg(self, message):
        """模拟消息发送"""
        print(f"[消息] {message}")

class MockTags:
    """模拟Tags对象"""
    def __init__(self):
        self._data = {}
    
    def set(self, key, value, category=None):
        full_key = f"{category}_{key}" if category else key
        self._data[full_key] = value
        return True
    
    def get(self, key, default=None, category=None):
        full_key = f"{category}_{key}" if category else key
        return self._data.get(full_key, default)
    
    def has(self, key, category=None):
        full_key = f"{category}_{key}" if category else key
        return full_key in self._data
    
    def all(self, category=None):
        if category:
            prefix = f"{category}_"
            return {k[len(prefix):]: v for k, v in self._data.items() if k.startswith(prefix)}
        return self._data.copy()

def test_handler_creation():
    """测试Handler创建和基础功能"""
    print("=== 测试Handler创建和基础功能 ===")
    
    try:
        from systems.handlers import (
            CultivationHandler, CombatSkillHandler, AlchemyHandler,
            KarmaHandler, AIDirectorHandler
        )
        
        # 创建模拟角色
        char = MockCharacter()
        
        # 测试各个Handler的创建
        handlers = {
            "修仙": CultivationHandler(char),
            "战斗技能": CombatSkillHandler(char),
            "炼丹": AlchemyHandler(char),
            "因果": KarmaHandler(char),
            "AI导演": AIDirectorHandler(char)
        }
        
        for name, handler in handlers.items():
            print(f"✓ {name}Handler创建成功: {handler}")
            
            # 测试基础方法
            if hasattr(handler, 'initialize'):
                handler.initialize()
                print(f"✓ {name}Handler初始化成功")
        
        return handlers
        
    except Exception as e:
        print(f"✗ Handler创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_cultivation_features(cultivation_handler):
    """测试修仙系统功能"""
    print("\n=== 测试修仙系统功能 ===")
    
    try:
        # 测试获取当前境界
        realm = cultivation_handler.get_current_realm()
        print(f"✓ 当前境界: {realm}")
        
        # 测试获取修炼进度
        progress = cultivation_handler.get_cultivation_progress()
        print(f"✓ 修炼进度: {progress}")
        
        # 测试修炼
        result = cultivation_handler.cultivate(duration=60)
        print(f"✓ 修炼结果: {result}")
        
        # 测试突破
        breakthrough_result = cultivation_handler.breakthrough()
        print(f"✓ 突破结果: {breakthrough_result}")
        
        return True
        
    except Exception as e:
        print(f"✗ 修仙系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combat_skill_features(combat_handler):
    """测试战斗技能功能"""
    print("\n=== 测试战斗技能功能 ===")
    
    try:
        # 测试获取可用技能
        skills = combat_handler.get_available_skills()
        print(f"✓ 可用技能: {list(skills.keys())[:3]}...")  # 只显示前3个
        
        # 测试学习技能
        if skills:
            skill_name = list(skills.keys())[0]
            result = combat_handler.learn_skill(skill_name)
            print(f"✓ 学习技能 '{skill_name}': {result}")
        
        # 测试获取已学技能
        learned = combat_handler.get_learned_skills()
        print(f"✓ 已学技能: {learned}")
        
        return True
        
    except Exception as e:
        print(f"✗ 战斗技能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alchemy_features(alchemy_handler):
    """测试炼丹功能"""
    print("\n=== 测试炼丹功能 ===")
    
    try:
        # 测试获取配方
        recipes = alchemy_handler.get_available_recipes()
        print(f"✓ 可用配方: {list(recipes.keys())[:3]}...")  # 只显示前3个
        
        # 测试获取材料
        materials = alchemy_handler.get_materials()
        print(f"✓ 拥有材料: {materials}")
        
        # 测试添加材料
        alchemy_handler.add_material("灵草", 10)
        alchemy_handler.add_material("灵石", 5)
        materials = alchemy_handler.get_materials()
        print(f"✓ 添加材料后: {materials}")
        
        return True
        
    except Exception as e:
        print(f"✗ 炼丹系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_karma_features(karma_handler):
    """测试因果功能"""
    print("\n=== 测试因果功能 ===")
    
    try:
        # 测试获取因果状态
        status = karma_handler.get_karma_status()
        print(f"✓ 因果状态: {status}")
        
        # 测试记录善行
        result = karma_handler.record_good_deed("帮助他人", 10)
        print(f"✓ 记录善行: {result}")
        
        # 测试记录恶行
        result = karma_handler.record_evil_deed("伤害无辜", 5)
        print(f"✓ 记录恶行: {result}")
        
        # 测试获取更新后的状态
        status = karma_handler.get_karma_status()
        print(f"✓ 更新后状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"✗ 因果系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_director_features(ai_handler):
    """测试AI导演功能"""
    print("\n=== 测试AI导演功能 ===")
    
    try:
        # 测试获取故事状态
        status = ai_handler.get_story_status()
        print(f"✓ 故事状态: {status}")
        
        # 测试更新上下文
        ai_handler.update_context("角色开始修炼")
        print(f"✓ 更新上下文成功")
        
        # 测试获取更新后状态
        status = ai_handler.get_story_status()
        print(f"✓ 更新后状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"✗ AI导演系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("修仙MUD Handler系统最终测试开始")
    print("=" * 50)
    
    # 创建Handler
    handlers = test_handler_creation()
    if not handlers:
        print("❌ Handler创建失败，停止测试")
        return False
    
    # 测试各个系统功能
    tests = [
        (test_cultivation_features, handlers["修仙"], "修仙系统"),
        (test_combat_skill_features, handlers["战斗技能"], "战斗技能系统"),
        (test_alchemy_features, handlers["炼丹"], "炼丹系统"),
        (test_karma_features, handlers["因果"], "因果系统"),
        (test_ai_director_features, handlers["AI导演"], "AI导演系统")
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func, handler, test_name in tests:
        try:
            if test_func(handler):
                passed += 1
                print(f"✓ {test_name}功能测试通过")
            else:
                print(f"✗ {test_name}功能测试失败")
        except Exception as e:
            print(f"✗ {test_name}功能测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"最终测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有Handler系统功能测试通过！")
        print("\n🚀 修仙MUD Day3-4系统集成完成！")
        print("✅ 事件驱动架构 - 完成")
        print("✅ TagProperty高性能查询 - 完成")
        print("✅ Handler生态组件化 - 完成")
        print("✅ 五大核心Handler - 完成")
        print("✅ 系统集成测试 - 完成")
        return True
    else:
        print("❌ 部分功能测试失败，需要进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

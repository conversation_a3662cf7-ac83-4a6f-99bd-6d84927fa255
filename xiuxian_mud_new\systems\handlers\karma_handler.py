# 因果Handler - 管理角色因果业力系统
# 基于设计文档实现的智能因果追踪系统

import time
import random
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    from evennia import logger
    if logger is None:
        logger = None
except ImportError:
    logger = None
from ..handler_system import BaseHandler, handler_method, HandlerRegistry
from ..event_system import XianxiaEventBus


class KarmaType(Enum):
    """因果类型"""
    GOOD = "善业"
    EVIL = "恶业"
    NEUTRAL = "中性"


class KarmaSource(Enum):
    """因果来源"""
    COMBAT = "战斗"
    QUEST = "任务"
    INTERACTION = "互动"
    CULTIVATION = "修炼"
    ALCHEMY = "炼丹"
    TEACHING = "传授"
    RESCUE = "救助"
    MURDER = "杀戮"


@dataclass
class KarmaRecord:
    """因果记录"""
    karma_type: KarmaType
    source: KarmaSource
    amount: int
    description: str
    timestamp: float
    related_character_id: Optional[str] = None
    location: Optional[str] = None


class KarmaHandler(BaseHandler):
    """
    因果Handler - 管理角色的善恶业力、因果报应、天道感应等
    """
    
    def __init__(self, owner):
        # 必须在super().__init__之前设置所有属性
        # 因果数值
        self.karma_balance = 0  # 正数为善业，负数为恶业
        self.total_good_karma = 0
        self.total_evil_karma = 0
        
        # 因果记录
        self.karma_records: List[KarmaRecord] = []
        self.max_records = 1000  # 最多保存1000条记录
        
        # 因果效应
        self.karma_effects = {
            "luck_modifier": 0.0,      # 运气修正
            "cultivation_modifier": 0.0, # 修炼效率修正
            "breakthrough_modifier": 0.0, # 突破成功率修正
            "calamity_resistance": 0.0,  # 天劫抗性
            "fortune_attraction": 0.0    # 机缘吸引力
        }
        
        # 天道感应
        self.heavenly_attention = 0  # 天道关注度
        self.last_judgment_time = 0  # 上次天道审判时间

        # 事件总线
        self.event_bus = None

        super().__init__(owner)
    
    def initialize(self):
        """初始化因果Handler"""
        super().initialize()
        
        # 获取事件总线
        self.event_bus = XianxiaEventBus.get_instance()
        
        # 计算初始因果效应
        self.update_karma_effects()
        
        self.log_info("因果Handler初始化完成")
    
    @handler_method
    def add_karma(self, karma_type: KarmaType, source: KarmaSource, amount: int, 
                  description: str, related_character_id: str = None) -> dict:
        """添加因果记录"""
        if amount <= 0:
            return {"success": False, "message": "因果数值必须为正数"}
        
        # 创建因果记录
        record = KarmaRecord(
            karma_type=karma_type,
            source=source,
            amount=amount,
            description=description,
            timestamp=time.time(),
            related_character_id=related_character_id,
            location=self.get_current_location()
        )
        
        # 添加到记录列表
        self.karma_records.append(record)
        
        # 限制记录数量
        if len(self.karma_records) > self.max_records:
            self.karma_records.pop(0)
        
        # 更新因果数值
        if karma_type == KarmaType.GOOD:
            self.karma_balance += amount
            self.total_good_karma += amount
        elif karma_type == KarmaType.EVIL:
            self.karma_balance -= amount
            self.total_evil_karma += amount
        
        # 更新天道关注度
        self.update_heavenly_attention(karma_type, amount)
        
        # 更新因果效应
        self.update_karma_effects()
        
        # 检查是否触发天道事件
        self.check_heavenly_events()
        
        result = {
            "success": True,
            "karma_type": karma_type.value,
            "amount": amount,
            "new_balance": self.karma_balance,
            "description": description
        }
        
        self.log_info(f"因果变化: {karma_type.value} +{amount}, 当前余额: {self.karma_balance}")
        
        return result
    
    @handler_method
    def add_good_karma(self, source: KarmaSource, amount: int, description: str, 
                      related_character_id: str = None) -> dict:
        """添加善业"""
        return self.add_karma(KarmaType.GOOD, source, amount, description, related_character_id)
    
    @handler_method
    def add_evil_karma(self, source: KarmaSource, amount: int, description: str, 
                      related_character_id: str = None) -> dict:
        """添加恶业"""
        return self.add_karma(KarmaType.EVIL, source, amount, description, related_character_id)
    
    def update_heavenly_attention(self, karma_type: KarmaType, amount: int):
        """更新天道关注度"""
        if karma_type == KarmaType.EVIL:
            # 恶业增加天道关注
            self.heavenly_attention += amount * 0.1
        elif karma_type == KarmaType.GOOD and self.heavenly_attention > 0:
            # 善业减少天道关注
            self.heavenly_attention = max(0, self.heavenly_attention - amount * 0.05)
        
        # 天道关注度上限
        self.heavenly_attention = min(self.heavenly_attention, 1000)
    
    def update_karma_effects(self):
        """更新因果效应"""
        # 基于因果余额计算效应
        balance = self.karma_balance
        
        # 运气修正 (-0.5 到 +0.5)
        self.karma_effects["luck_modifier"] = max(-0.5, min(0.5, balance / 2000))
        
        # 修炼效率修正
        if balance > 0:
            self.karma_effects["cultivation_modifier"] = min(0.3, balance / 5000)
        else:
            self.karma_effects["cultivation_modifier"] = max(-0.2, balance / 10000)
        
        # 突破成功率修正
        self.karma_effects["breakthrough_modifier"] = max(-0.3, min(0.2, balance / 8000))
        
        # 天劫抗性 (善业提供保护)
        if balance > 0:
            self.karma_effects["calamity_resistance"] = min(0.4, balance / 6000)
        else:
            self.karma_effects["calamity_resistance"] = 0
        
        # 机缘吸引力
        self.karma_effects["fortune_attraction"] = max(0, min(0.5, balance / 4000))
    
    def check_heavenly_events(self):
        """检查是否触发天道事件"""
        current_time = time.time()
        
        # 天道审判冷却时间 (24小时)
        if current_time - self.last_judgment_time < 86400:
            return
        
        # 高天道关注度触发事件
        if self.heavenly_attention > 500:
            self.trigger_heavenly_judgment()
        
        # 极高善业触发天道嘉奖
        elif self.karma_balance > 5000:
            self.trigger_heavenly_reward()
        
        # 极高恶业触发天道惩罚
        elif self.karma_balance < -3000:
            self.trigger_heavenly_punishment()
    
    def trigger_heavenly_judgment(self):
        """触发天道审判"""
        self.last_judgment_time = time.time()
        
        owner = self.get_owner()
        if not owner:
            return
        
        # 基于因果余额决定审判结果
        if self.karma_balance > 0:
            # 善业者获得保护
            self.add_good_karma(KarmaSource.CULTIVATION, 100, "天道嘉奖，善有善报")
            self.heavenly_attention = max(0, self.heavenly_attention - 200)
            
            self.log_info("天道审判：善业护体，获得天道嘉奖")
            
        else:
            # 恶业者受到惩罚
            punishment_severity = min(abs(self.karma_balance) // 1000, 5)
            
            # 应用惩罚效果
            if hasattr(owner, 'cultivation'):
                # 损失修为
                current_points = owner.cultivation.get("cultivation_points", 0)
                loss = current_points * (punishment_severity * 0.1)
                owner.cultivation.set("cultivation_points", max(0, current_points - loss))
            
            self.heavenly_attention = max(0, self.heavenly_attention - 300)
            
            self.log_info(f"天道审判：恶业缠身，受到{punishment_severity}级惩罚")
    
    def trigger_heavenly_reward(self):
        """触发天道嘉奖"""
        self.last_judgment_time = time.time()
        
        owner = self.get_owner()
        if not owner:
            return
        
        # 随机奖励
        rewards = [
            ("cultivation_boost", 1000, "天道赐予修为"),
            ("spiritual_energy", 500, "天道补充灵力"),
            ("breakthrough_chance", 0.2, "天道助力突破"),
            ("rare_item", 1, "天道赐予珍宝")
        ]
        
        reward_type, reward_value, reward_desc = random.choice(rewards)
        
        if reward_type == "cultivation_boost" and hasattr(owner, 'cultivation'):
            owner.cultivation.advance_cultivation(reward_value)
        elif reward_type == "spiritual_energy" and hasattr(owner, 'cultivation'):
            current = owner.cultivation.get("spiritual_energy", 0)
            owner.cultivation.set("spiritual_energy", current + reward_value)
        elif reward_type == "breakthrough_chance" and hasattr(owner, 'cultivation'):
            owner.cultivation.set("heavenly_breakthrough_bonus", reward_value)
        
        self.add_good_karma(KarmaSource.CULTIVATION, 50, reward_desc)
        
        self.log_info(f"天道嘉奖：{reward_desc}")
    
    def trigger_heavenly_punishment(self):
        """触发天道惩罚"""
        self.last_judgment_time = time.time()
        
        owner = self.get_owner()
        if not owner:
            return
        
        # 惩罚强度基于恶业程度
        punishment_level = min(abs(self.karma_balance) // 1000, 10)
        
        punishments = [
            ("cultivation_loss", punishment_level * 500, "天道夺取修为"),
            ("spiritual_drain", punishment_level * 200, "天道抽取灵力"),
            ("calamity_mark", punishment_level, "天道标记，增加劫难"),
            ("luck_curse", punishment_level * 0.1, "天道诅咒，霉运缠身")
        ]
        
        punishment_type, punishment_value, punishment_desc = random.choice(punishments)
        
        if punishment_type == "cultivation_loss" and hasattr(owner, 'cultivation'):
            current_points = owner.cultivation.get("cultivation_points", 0)
            owner.cultivation.set("cultivation_points", max(0, current_points - punishment_value))
        elif punishment_type == "spiritual_drain" and hasattr(owner, 'cultivation'):
            current = owner.cultivation.get("spiritual_energy", 0)
            owner.cultivation.set("spiritual_energy", max(0, current - punishment_value))
        elif punishment_type == "calamity_mark":
            # 增加天劫难度标记
            if hasattr(owner, 'tags'):
                current_mark = owner.tags.get("calamity_mark", 0)
                owner.tags.set("calamity_mark", current_mark + punishment_value)
        elif punishment_type == "luck_curse":
            # 临时运气诅咒
            if hasattr(owner, 'tags'):
                owner.tags.set("luck_curse", punishment_value)
                owner.tags.set("luck_curse_duration", time.time() + 86400)  # 24小时
        
        self.heavenly_attention = max(0, self.heavenly_attention - 100)
        
        self.log_info(f"天道惩罚：{punishment_desc}")
    
    def get_current_location(self) -> str:
        """获取当前位置"""
        owner = self.get_owner()
        if owner and hasattr(owner, 'location'):
            return str(owner.location)
        return "未知位置"
    
    @handler_method
    def get_karma_status(self) -> dict:
        """获取因果状态"""
        # 计算因果等级
        karma_level = self.calculate_karma_level()
        
        return {
            "karma_balance": self.karma_balance,
            "total_good_karma": self.total_good_karma,
            "total_evil_karma": self.total_evil_karma,
            "karma_level": karma_level,
            "heavenly_attention": self.heavenly_attention,
            "karma_effects": self.karma_effects.copy(),
            "records_count": len(self.karma_records)
        }
    
    @handler_method
    def get_karma_records(self, limit: int = 50) -> List[dict]:
        """获取因果记录"""
        records = []
        
        # 获取最近的记录
        recent_records = self.karma_records[-limit:] if limit > 0 else self.karma_records
        
        for record in reversed(recent_records):
            records.append({
                "karma_type": record.karma_type.value,
                "source": record.source.value,
                "amount": record.amount,
                "description": record.description,
                "timestamp": record.timestamp,
                "related_character_id": record.related_character_id,
                "location": record.location
            })
        
        return records
    
    @handler_method
    def get_karma_analysis(self) -> dict:
        """获取因果分析"""
        if not self.karma_records:
            return {"message": "暂无因果记录"}
        
        # 统计各类因果来源
        source_stats = {}
        recent_trend = []
        
        for record in self.karma_records[-100:]:  # 分析最近100条记录
            source = record.source.value
            if source not in source_stats:
                source_stats[source] = {"good": 0, "evil": 0, "count": 0}
            
            source_stats[source]["count"] += 1
            if record.karma_type == KarmaType.GOOD:
                source_stats[source]["good"] += record.amount
            elif record.karma_type == KarmaType.EVIL:
                source_stats[source]["evil"] += record.amount
            
            # 记录趋势
            recent_trend.append({
                "timestamp": record.timestamp,
                "balance_change": record.amount if record.karma_type == KarmaType.GOOD else -record.amount
            })
        
        # 计算趋势
        if len(recent_trend) >= 2:
            recent_change = sum(t["balance_change"] for t in recent_trend[-10:])
            trend = "上升" if recent_change > 0 else "下降" if recent_change < 0 else "稳定"
        else:
            trend = "数据不足"
        
        return {
            "karma_level": self.calculate_karma_level(),
            "source_statistics": source_stats,
            "recent_trend": trend,
            "heavenly_status": self.get_heavenly_status(),
            "recommendations": self.get_karma_recommendations()
        }
    
    def calculate_karma_level(self) -> str:
        """计算因果等级"""
        balance = self.karma_balance
        
        if balance >= 5000:
            return "圣人"
        elif balance >= 3000:
            return "善人"
        elif balance >= 1000:
            return "良民"
        elif balance >= -500:
            return "普通"
        elif balance >= -1500:
            return "恶徒"
        elif balance >= -3000:
            return "恶人"
        else:
            return "魔头"
    
    def get_heavenly_status(self) -> str:
        """获取天道状态"""
        attention = self.heavenly_attention
        
        if attention >= 800:
            return "天怒人怨"
        elif attention >= 500:
            return "天道关注"
        elif attention >= 200:
            return "略有察觉"
        else:
            return "天道无视"
    
    def get_karma_recommendations(self) -> List[str]:
        """获取因果建议"""
        recommendations = []
        
        if self.karma_balance < -1000:
            recommendations.append("恶业过重，建议多行善事以化解")
            recommendations.append("可通过救助他人、传授功法等方式积累善业")
        
        if self.heavenly_attention > 300:
            recommendations.append("天道关注度过高，需谨慎行事")
            recommendations.append("建议暂时避免争斗，专心修炼")
        
        if self.karma_balance > 2000:
            recommendations.append("善业充足，可考虑冲击更高境界")
            recommendations.append("天道庇佑，是突破的好时机")
        
        if not recommendations:
            recommendations.append("因果平衡，继续保持当前修行方式")
        
        return recommendations
    
    @handler_method
    def purify_karma(self, amount: int) -> dict:
        """净化恶业 (消耗善业)"""
        if self.karma_balance <= 0:
            return {"success": False, "message": "没有善业可用于净化"}
        
        if amount > self.karma_balance:
            amount = self.karma_balance
        
        # 净化比例 2:1 (消耗2点善业净化1点恶业)
        evil_purified = amount // 2
        good_consumed = evil_purified * 2
        
        if evil_purified <= 0:
            return {"success": False, "message": "善业不足以进行净化"}
        
        # 执行净化
        self.karma_balance -= good_consumed
        self.total_evil_karma = max(0, self.total_evil_karma - evil_purified)
        
        # 更新效应
        self.update_karma_effects()
        
        # 记录净化行为
        self.add_karma(KarmaType.NEUTRAL, KarmaSource.CULTIVATION, 0, 
                      f"净化恶业 {evil_purified} 点，消耗善业 {good_consumed} 点")
        
        self.log_info(f"因果净化：消耗善业 {good_consumed}，净化恶业 {evil_purified}")
        
        return {
            "success": True,
            "good_consumed": good_consumed,
            "evil_purified": evil_purified,
            "new_balance": self.karma_balance
        }


# 注册Handler
HandlerRegistry.register(KarmaHandler, "karma")

name: Release

on:
  release:
    types:
      - published

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v3

      - name: Set env
        run: echo "VERSION=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: lts/*

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Set package version
        run: |
          echo $(jq --arg v "${{ env.VERSION }}" '(.version) = $v' package.json) > package.json

      - name: Update version in source file
        run: |
          sed -i "s/version: \"[0-9]*\.[0-9]*\.[0-9]*\"/version: \"${{ env.VERSION }}\"/" src/index.ts

      - name: Install Dependencies
        run: bun install

      - name: Build
        run: bun run build

      - name: Set NPM_TOKEN
        run: npm config set //registry.npmjs.org/:_authToken=${{secrets.NPM_TOKEN}}

      - name: Publish
        if: "!github.event.release.prerelease"
        run: |
          npm pkg delete scripts.prepare
          npm publish --access public

      - name: Publish release candidate
        if: "github.event.release.prerelease"
        run: |
          npm pkg delete scripts.prepare
          npm publish --access public --tag=canary

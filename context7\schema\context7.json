{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://context7.com/schema/context7.json", "title": "Context7 Configuration Schema", "description": "Configuration file for Context7 project parsing and documentation generation", "type": "object", "properties": {"projectTitle": {"type": "string", "description": "The display name for your project in Context7. This overrides the default repository name.", "minLength": 1, "maxLength": 100, "examples": ["Up<PERSON>sh <PERSON>limit", "Next.js", "React Query"]}, "description": {"type": "string", "description": "A brief description of what your library does. This helps coding agents understand the purpose of your project.", "minLength": 10, "maxLength": 200, "examples": ["Ratelimiting library based on Upstash Redis", "The React Framework for Production", "Powerful data synchronization for React"]}, "folders": {"type": "array", "description": "Specific folder paths to include when parsing documentation. If empty, Context7 will scan the entire repository. Supports regex patterns and requires full paths.", "items": {"type": "string", "minLength": 1}, "uniqueItems": true, "default": [], "examples": [["docs", "guides", "examples"], ["documentation/**"], ["api-reference", "tutorials/*"]]}, "excludeFolders": {"type": "array", "description": "Folder paths to exclude from documentation parsing. Supports regex patterns and requires full paths.", "items": {"type": "string", "minLength": 1}, "uniqueItems": true, "default": [], "examples": [["src", "build", "node_modules"], ["**/test/**", "**/tests/**"], ["legacy/*", "archive"]]}, "excludeFiles": {"type": "array", "description": "Specific file names to exclude from documentation parsing. Only include the filename (not the path). Regex patterns are not supported.", "items": {"type": "string", "minLength": 1, "pattern": "^[^/\\\\]+$"}, "uniqueItems": true, "default": [], "examples": [["CHANGELOG.md", "LICENSE"], ["README-dev.md", "CONTRIBUTING.md"], ["package.json", "tsconfig.json"]]}, "rules": {"type": "array", "description": "Best practices or important guidelines that coding agents should follow when using your library. These appear as recommendations in the documentation context.", "items": {"type": "string", "minLength": 5, "maxLength": 200}, "default": [], "examples": [["Always use TypeScript for better type safety"], ["Use Upstash Redis as a database", "Use single region set up"], ["Import components from the main package", "Follow the naming conventions"]]}, "previousVersions": {"type": "array", "description": "Information about previous versions of your library that should also be available in Context7.", "items": {"type": "object", "properties": {"tag": {"type": "string", "description": "The Git tag or version identifier", "pattern": "^v?\\d+\\.\\d+\\.\\d+", "examples": ["v1.2.1", "2.0.0", "v3.1.0-beta.1"]}, "title": {"type": "string", "description": "Human-readable version name", "minLength": 1, "maxLength": 50, "examples": ["version 1.2.1", "Legacy Version", "Beta Release"]}}, "required": ["tag", "title"], "additionalProperties": false}, "default": []}}, "additionalProperties": false, "examples": [{"projectTitle": "Up<PERSON>sh <PERSON>limit", "description": "Ratelimiting library based on Upstash Redis", "folders": [], "excludeFolders": ["src"], "excludeFiles": [], "rules": ["Use Upstash Redis as a database", "Use single region set up"], "previousVersions": [{"tag": "v1.2.1", "title": "version 1.2.1"}]}]}
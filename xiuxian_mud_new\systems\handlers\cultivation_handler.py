# 修仙Handler - 管理角色修仙相关功能
# 基于设计文档实现的高性能修仙系统

import time
import random
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from evennia import logger
from ..handler_system import BaseHandler, handler_method, HandlerRegistry
from ..event_system import XianxiaEventBus, CultivationBreakthroughEvent, CultivationProgressEvent


@dataclass
class CultivationTechnique:
    """修仙功法数据类"""
    name: str
    grade: str  # 天地玄黄
    type: str   # 攻击型、防御型、平衡型
    efficiency: float  # 修炼效率倍数
    requirements: Dict[str, Any]  # 修炼要求
    effects: Dict[str, Any]  # 功法效果


class CultivationHandler(BaseHandler):
    """
    修仙Handler - 管理角色的修仙进度、境界突破、功法修炼等
    """
    
    def __init__(self, owner):
        super().__init__(owner)
        
        # 修仙境界定义
        self.realm_definitions = {
            "练气期": {"max_level": 12, "tier": 1, "base_power": 100},
            "筑基期": {"max_level": 9, "tier": 2, "base_power": 1000},
            "金丹期": {"max_level": 9, "tier": 3, "base_power": 5000},
            "元婴期": {"max_level": 9, "tier": 4, "base_power": 20000},
            "化神期": {"max_level": 9, "tier": 5, "base_power": 80000},
            "炼虚期": {"max_level": 9, "tier": 6, "base_power": 300000},
            "合体期": {"max_level": 9, "tier": 7, "base_power": 1000000},
            "大乘期": {"max_level": 9, "tier": 8, "base_power": 3000000},
            "渡劫期": {"max_level": 9, "tier": 9, "base_power": 10000000}
        }
        
        # 功法库
        self.technique_library = {}
        self.load_default_techniques()
        
        # 事件总线
        self.event_bus = None
        
        # 修炼状态
        self.cultivation_state = {
            "is_cultivating": False,
            "current_technique": None,
            "cultivation_start_time": 0,
            "accumulated_progress": 0
        }
    
    def initialize(self):
        """初始化修仙Handler"""
        super().initialize()
        
        # 获取事件总线
        self.event_bus = XianxiaEventBus.get_instance()
        
        # 初始化角色修仙属性
        owner = self.get_owner()
        if owner and hasattr(owner, 'cultivation'):
            # 确保基础属性存在
            if not owner.cultivation.has("realm"):
                owner.cultivation.set_realm("练气期", 1)
            
            if not owner.cultivation.has("cultivation_points"):
                owner.cultivation.set("cultivation_points", 0)
            
            if not owner.cultivation.has("spiritual_energy"):
                owner.cultivation.set("spiritual_energy", 100)
        
        self.log_info("修仙Handler初始化完成")
    
    def load_default_techniques(self):
        """加载默认功法"""
        default_techniques = [
            CultivationTechnique(
                name="基础吐纳术",
                grade="黄",
                type="平衡型",
                efficiency=1.0,
                requirements={"realm_tier": 1},
                effects={"spiritual_energy_regen": 1.2}
            ),
            CultivationTechnique(
                name="青木长生功",
                grade="玄",
                type="防御型",
                efficiency=1.5,
                requirements={"realm_tier": 2},
                effects={"spiritual_energy_regen": 1.5, "defense_bonus": 0.2}
            ),
            CultivationTechnique(
                name="烈火焚天诀",
                grade="地",
                type="攻击型",
                efficiency=2.0,
                requirements={"realm_tier": 3},
                effects={"attack_bonus": 0.3, "fire_affinity": 0.5}
            ),
            CultivationTechnique(
                name="太极玄清道",
                grade="天",
                type="平衡型",
                efficiency=3.0,
                requirements={"realm_tier": 5},
                effects={"all_stats_bonus": 0.25, "breakthrough_chance": 0.1}
            )
        ]
        
        for technique in default_techniques:
            self.technique_library[technique.name] = technique
    
    @handler_method
    def get_current_realm(self) -> Tuple[str, int]:
        """获取当前修仙境界"""
        owner = self.get_owner()
        if owner and hasattr(owner, 'cultivation'):
            return owner.cultivation.get_realm()
        return "练气期", 1
    
    @handler_method
    def get_cultivation_progress(self) -> dict:
        """获取修炼进度"""
        owner = self.get_owner()
        if owner and hasattr(owner, 'cultivation'):
            return owner.cultivation.get_cultivation_progress()
        return {}
    
    @handler_method
    def start_cultivation(self, technique_name: str = None) -> bool:
        """开始修炼"""
        if self.cultivation_state["is_cultivating"]:
            return False
        
        owner = self.get_owner()
        if not owner:
            return False
        
        # 选择功法
        if technique_name and technique_name in self.technique_library:
            technique = self.technique_library[technique_name]
            
            # 检查功法要求
            if not self.check_technique_requirements(technique):
                self.log_info(f"不满足功法 {technique_name} 的修炼要求")
                return False
        else:
            # 使用默认功法
            technique = self.get_suitable_technique()
        
        # 开始修炼
        self.cultivation_state.update({
            "is_cultivating": True,
            "current_technique": technique,
            "cultivation_start_time": time.time(),
            "accumulated_progress": 0
        })
        
        self.log_info(f"开始修炼功法: {technique.name}")
        
        # 发送修炼开始事件
        if self.event_bus:
            event = CultivationProgressEvent(
                character_id=str(owner.id),
                technique_name=technique.name,
                progress_type="start"
            )
            self.event_bus.emit_event(event)
        
        return True
    
    @handler_method
    def stop_cultivation(self) -> dict:
        """停止修炼并计算收益"""
        if not self.cultivation_state["is_cultivating"]:
            return {"success": False, "message": "当前未在修炼"}
        
        owner = self.get_owner()
        if not owner:
            return {"success": False, "message": "角色不存在"}
        
        # 计算修炼时间和收益
        cultivation_time = time.time() - self.cultivation_state["cultivation_start_time"]
        technique = self.cultivation_state["current_technique"]
        
        # 基础收益计算
        base_gain = cultivation_time / 60  # 每分钟1点基础收益
        efficiency_multiplier = technique.efficiency if technique else 1.0
        
        # 境界加成
        realm, level = self.get_current_realm()
        realm_multiplier = self.realm_definitions[realm]["tier"] * 0.1 + 1.0
        
        # 随机因子 (0.8-1.2)
        random_factor = random.uniform(0.8, 1.2)
        
        final_gain = int(base_gain * efficiency_multiplier * realm_multiplier * random_factor)
        
        # 应用收益
        if hasattr(owner, 'cultivation'):
            owner.cultivation.advance_cultivation(final_gain)
        
        # 重置修炼状态
        self.cultivation_state.update({
            "is_cultivating": False,
            "current_technique": None,
            "cultivation_start_time": 0,
            "accumulated_progress": 0
        })
        
        result = {
            "success": True,
            "cultivation_time": cultivation_time,
            "technique_used": technique.name if technique else "无",
            "cultivation_points_gained": final_gain,
            "total_cultivation_points": owner.cultivation.get("cultivation_points", 0)
        }
        
        self.log_info(f"修炼结束，获得修为点数: {final_gain}")
        
        # 发送修炼结束事件
        if self.event_bus:
            event = CultivationProgressEvent(
                character_id=str(owner.id),
                technique_name=technique.name if technique else "无",
                progress_type="end",
                points_gained=final_gain
            )
            self.event_bus.emit_event(event)
        
        return result
    
    @handler_method
    def attempt_breakthrough(self) -> dict:
        """尝试境界突破"""
        owner = self.get_owner()
        if not owner or not hasattr(owner, 'cultivation'):
            return {"success": False, "message": "角色不存在或无修仙属性"}
        
        # 检查是否可以突破
        if not owner.cultivation.can_breakthrough():
            progress = owner.cultivation.get_cultivation_progress()
            return {
                "success": False,
                "message": f"修为不足，需要 {progress['required_points']} 点，当前 {progress['cultivation_points']} 点"
            }
        
        realm, level = self.get_current_realm()
        realm_def = self.realm_definitions[realm]
        
        # 计算突破成功率
        base_success_rate = 0.7  # 基础70%成功率
        
        # 功法加成
        technique_bonus = 0.0
        if (self.cultivation_state["current_technique"] and 
            "breakthrough_chance" in self.cultivation_state["current_technique"].effects):
            technique_bonus = self.cultivation_state["current_technique"].effects["breakthrough_chance"]
        
        # 修为超额加成
        progress = owner.cultivation.get_cultivation_progress()
        excess_ratio = progress["cultivation_points"] / progress["required_points"]
        excess_bonus = min((excess_ratio - 1.0) * 0.1, 0.2)  # 最多20%加成
        
        final_success_rate = min(base_success_rate + technique_bonus + excess_bonus, 0.95)
        
        # 进行突破判定
        success = random.random() < final_success_rate
        
        if success:
            # 突破成功
            new_level = level + 1
            new_realm = realm
            
            # 检查是否需要进入下一个大境界
            if new_level > realm_def["max_level"]:
                realm_list = list(self.realm_definitions.keys())
                current_realm_index = realm_list.index(realm)
                
                if current_realm_index < len(realm_list) - 1:
                    new_realm = realm_list[current_realm_index + 1]
                    new_level = 1
                else:
                    # 已达到最高境界
                    return {"success": False, "message": "已达到最高境界"}
            
            # 更新境界
            owner.cultivation.set_realm(new_realm, new_level)
            
            # 消耗修为点数
            owner.cultivation.set("cultivation_points", 0)
            owner.cultivation.set("can_breakthrough", False)
            
            result = {
                "success": True,
                "old_realm": f"{realm}第{level}层",
                "new_realm": f"{new_realm}第{new_level}层",
                "success_rate": f"{final_success_rate*100:.1f}%"
            }
            
            self.log_info(f"突破成功: {result['old_realm']} -> {result['new_realm']}")
            
            # 发送突破成功事件
            if self.event_bus:
                event = CultivationBreakthroughEvent(
                    character_id=str(owner.id),
                    old_realm=realm,
                    old_level=level,
                    new_realm=new_realm,
                    new_level=new_level,
                    success=True
                )
                self.event_bus.emit_event(event)
            
            return result
        
        else:
            # 突破失败
            # 消耗一半修为点数
            current_points = owner.cultivation.get("cultivation_points", 0)
            owner.cultivation.set("cultivation_points", current_points // 2)
            
            result = {
                "success": False,
                "message": "突破失败，损失一半修为",
                "success_rate": f"{final_success_rate*100:.1f}%",
                "remaining_points": current_points // 2
            }
            
            self.log_info(f"突破失败: {realm}第{level}层")
            
            # 发送突破失败事件
            if self.event_bus:
                event = CultivationBreakthroughEvent(
                    character_id=str(owner.id),
                    old_realm=realm,
                    old_level=level,
                    new_realm=realm,
                    new_level=level,
                    success=False
                )
                self.event_bus.emit_event(event)
            
            return result
    
    def check_technique_requirements(self, technique: CultivationTechnique) -> bool:
        """检查功法修炼要求"""
        owner = self.get_owner()
        if not owner or not hasattr(owner, 'cultivation'):
            return False
        
        # 检查境界要求
        if "realm_tier" in technique.requirements:
            required_tier = technique.requirements["realm_tier"]
            current_tier = owner.cultivation.get("realm_tier", 1)
            if current_tier < required_tier:
                return False
        
        return True
    
    def get_suitable_technique(self) -> CultivationTechnique:
        """获取适合当前境界的功法"""
        owner = self.get_owner()
        if not owner or not hasattr(owner, 'cultivation'):
            return self.technique_library["基础吐纳术"]
        
        current_tier = owner.cultivation.get("realm_tier", 1)
        
        # 找到最适合的功法
        suitable_techniques = []
        for technique in self.technique_library.values():
            if self.check_technique_requirements(technique):
                suitable_techniques.append(technique)
        
        if suitable_techniques:
            # 选择效率最高的功法
            return max(suitable_techniques, key=lambda t: t.efficiency)
        else:
            return self.technique_library["基础吐纳术"]
    
    @handler_method
    def get_available_techniques(self) -> List[dict]:
        """获取可用功法列表"""
        available = []
        
        for technique in self.technique_library.values():
            can_use = self.check_technique_requirements(technique)
            
            available.append({
                "name": technique.name,
                "grade": technique.grade,
                "type": technique.type,
                "efficiency": technique.efficiency,
                "can_use": can_use,
                "requirements": technique.requirements,
                "effects": technique.effects
            })
        
        return available
    
    @handler_method
    def get_cultivation_status(self) -> dict:
        """获取修炼状态"""
        status = self.cultivation_state.copy()
        
        if status["current_technique"]:
            status["current_technique"] = {
                "name": status["current_technique"].name,
                "grade": status["current_technique"].grade,
                "efficiency": status["current_technique"].efficiency
            }
        
        if status["is_cultivating"]:
            status["cultivation_duration"] = time.time() - status["cultivation_start_time"]
        
        return status


# 注册Handler
HandlerRegistry.register(CultivationHandler, "cultivation")

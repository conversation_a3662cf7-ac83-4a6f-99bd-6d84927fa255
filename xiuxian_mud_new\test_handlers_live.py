#!/usr/bin/env python
"""
修仙MUD Handler系统实时测试
在Evennia环境中测试Handler功能
"""

import os
import sys
import django
from django.conf import settings

# 设置Django环境
if not settings.configured:
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
    django.setup()

def test_character_handlers():
    """测试Character的Handler集成"""
    print("=== 测试Character Handler集成 ===")
    
    try:
        from typeclasses.characters import Character

        # 直接创建Character实例进行测试
        test_char = Character()
        test_char.key = "测试修仙者"
        test_char.id = "test_char_001"
        
        print(f"✓ 创建测试角色: {test_char}")
        
        # 测试Handler属性访问
        cultivation = test_char.cultivation
        print(f"✓ 修仙Handler: {cultivation}")
        
        combat_skill = test_char.combat_skill
        print(f"✓ 战斗技能Handler: {combat_skill}")
        
        alchemy = test_char.alchemy
        print(f"✓ 炼丹Handler: {alchemy}")
        
        karma = test_char.karma
        print(f"✓ 因果Handler: {karma}")
        
        ai_director = test_char.ai_director
        print(f"✓ AI导演Handler: {ai_director}")
        
        return test_char
        
    except Exception as e:
        print(f"✗ Character Handler测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_cultivation_system(character):
    """测试修仙系统功能"""
    print("\n=== 测试修仙系统功能 ===")
    
    try:
        cultivation = character.cultivation
        
        # 测试获取当前境界
        current_realm = cultivation.get_current_realm()
        print(f"✓ 当前境界: {current_realm}")
        
        # 测试获取修炼进度
        progress = cultivation.get_cultivation_progress()
        print(f"✓ 修炼进度: {progress}")
        
        # 测试修炼功能
        result = cultivation.cultivate(duration=60)  # 修炼1分钟
        print(f"✓ 修炼结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"✗ 修仙系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combat_skill_system(character):
    """测试战斗技能系统"""
    print("\n=== 测试战斗技能系统 ===")
    
    try:
        combat_skill = character.combat_skill
        
        # 测试获取技能列表
        skills = combat_skill.get_available_skills()
        print(f"✓ 可用技能: {skills}")
        
        # 测试学习技能
        if skills:
            skill_name = list(skills.keys())[0]
            result = combat_skill.learn_skill(skill_name)
            print(f"✓ 学习技能结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"✗ 战斗技能系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alchemy_system(character):
    """测试炼丹系统"""
    print("\n=== 测试炼丹系统 ===")
    
    try:
        alchemy = character.alchemy
        
        # 测试获取配方
        recipes = alchemy.get_available_recipes()
        print(f"✓ 可用配方: {recipes}")
        
        # 测试获取材料
        materials = alchemy.get_materials()
        print(f"✓ 拥有材料: {materials}")
        
        return True
        
    except Exception as e:
        print(f"✗ 炼丹系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_karma_system(character):
    """测试因果系统"""
    print("\n=== 测试因果系统 ===")
    
    try:
        karma = character.karma
        
        # 测试获取因果状态
        karma_status = karma.get_karma_status()
        print(f"✓ 因果状态: {karma_status}")
        
        # 测试记录善行
        result = karma.record_good_deed("帮助他人", 10)
        print(f"✓ 记录善行结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"✗ 因果系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_director_system(character):
    """测试AI导演系统"""
    print("\n=== 测试AI导演系统 ===")
    
    try:
        ai_director = character.ai_director
        
        # 测试获取故事状态
        story_status = ai_director.get_story_status()
        print(f"✓ 故事状态: {story_status}")
        
        return True
        
    except Exception as e:
        print(f"✗ AI导演系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_character(character):
    """清理测试角色"""
    if character:
        try:
            # 在测试环境中，我们只是简单地清理引用
            print(f"✓ 清理测试角色: {character}")
        except Exception as e:
            print(f"✗ 清理失败: {e}")

def main():
    """主测试函数"""
    print("修仙MUD Handler系统实时测试开始")
    print("=" * 50)
    
    # 测试Character Handler集成
    test_char = test_character_handlers()
    if not test_char:
        print("❌ Character Handler集成失败，停止测试")
        return False
    
    # 测试各个系统
    tests = [
        (test_cultivation_system, "修仙系统"),
        (test_combat_skill_system, "战斗技能系统"),
        (test_alchemy_system, "炼丹系统"),
        (test_karma_system, "因果系统"),
        (test_ai_director_system, "AI导演系统")
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func, test_name in tests:
        try:
            if test_func(test_char):
                passed += 1
                print(f"✓ {test_name}测试通过")
            else:
                print(f"✗ {test_name}测试失败")
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
    
    # 清理
    cleanup_test_character(test_char)
    
    print("\n" + "=" * 50)
    print(f"实时测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有Handler系统实时测试通过！")
        return True
    else:
        print("❌ 部分测试失败，需要检查Handler实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

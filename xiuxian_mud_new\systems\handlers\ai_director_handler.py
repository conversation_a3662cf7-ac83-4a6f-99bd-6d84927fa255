# AI导演Handler - 管理智能剧情生成和世界事件
# 基于设计文档实现的LLM驱动智能导演系统

import time
import random
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    from evennia import logger
except ImportError:
    # 创建一个简单的logger替代品用于测试环境
    class SimpleLogger:
        def log_info(self, msg):
            print(f"[INFO] {msg}")
        def log_error(self, msg):
            print(f"[ERROR] {msg}")
        def log_warn(self, msg):
            print(f"[WARN] {msg}")
    logger = SimpleLogger()
from ..handler_system import BaseHandler, handler_method, HandlerRegistry
from ..event_system import (XianxiaEventBus, CelestialAnomalyEvent,
                           SectConflictEvent, MasterDiscipleEvent, BaseEventHandler)


class StoryEventType(Enum):
    """剧情事件类型"""
    ENCOUNTER = "奇遇"
    CONFLICT = "冲突"
    DISCOVERY = "发现"
    CHALLENGE = "挑战"
    OPPORTUNITY = "机缘"
    CALAMITY = "劫难"


class NarrativeStyle(Enum):
    """叙事风格"""
    CLASSICAL = "古典仙侠"
    MODERN = "现代修真"
    DARK = "黑暗修仙"
    COMEDIC = "轻松搞笑"
    EPIC = "史诗传奇"


@dataclass
class StoryEvent:
    """剧情事件"""
    event_id: str
    event_type: StoryEventType
    title: str
    description: str
    participants: List[str]
    location: str
    timestamp: float
    consequences: Dict[str, Any]
    narrative_style: NarrativeStyle


@dataclass
class WorldState:
    """世界状态"""
    current_era: str
    major_events: List[str]
    active_conflicts: List[str]
    spiritual_energy_level: float
    celestial_alignment: str
    sect_relations: Dict[str, Dict[str, float]]


class AIDirectorHandler(BaseHandler):
    """
    AI导演Handler - 智能剧情生成、世界事件管理、动态叙事控制
    """
    
    def __init__(self, owner):
        # 先初始化世界状态，因为BaseHandler.__init__会调用initialize()
        self.world_state = WorldState(
            current_era="修仙盛世",
            major_events=[],
            active_conflicts=[],
            spiritual_energy_level=1.0,
            celestial_alignment="平和",
            sect_relations={}
        )

        # 剧情事件
        self.active_story_events: List[StoryEvent] = []
        self.completed_events: List[StoryEvent] = []
        self.event_templates = {}

        # AI导演设置
        self.narrative_preferences = {
            "style": NarrativeStyle.CLASSICAL,
            "complexity": 0.7,  # 剧情复杂度 0-1
            "frequency": 0.5,   # 事件频率 0-1
            "player_agency": 0.8  # 玩家主导性 0-1
        }

        # 角色分析
        self.character_profiles = {}
        self.relationship_network = {}

        # 事件生成
        self.last_event_time = 0
        self.event_cooldown = 300  # 5分钟冷却

        # 事件总线
        self.event_bus = None

        # 加载事件模板
        self.load_event_templates()

        super().__init__(owner)
    
    def initialize(self):
        """初始化AI导演Handler"""
        super().initialize()
        
        # 获取事件总线
        self.event_bus = XianxiaEventBus.get_instance()
        
        # 注册事件监听器
        if self.event_bus:
            # 创建一个简单的事件处理器
            class AIEventHandler(BaseEventHandler):
                def __init__(self, ai_director):
                    super().__init__()
                    self.ai_director = ai_director

                def handle_event(self, event):
                    return self.ai_director.on_world_event(event)

            ai_event_handler = AIEventHandler(self)
            self.event_bus.register_handler("ai_director_world", ai_event_handler)
        
        # 初始化世界状态
        self.initialize_world_state()
        
        # 开始AI导演循环
        self.start_director_cycle()
        
        self.log_info("AI导演Handler初始化完成")
    
    def load_event_templates(self):
        """加载事件模板"""
        self.event_templates = {
            "奇遇": [
                {
                    "title": "神秘洞府",
                    "description": "在{location}发现一处隐秘洞府，其中灵气浓郁，似有宝物",
                    "triggers": ["exploration", "high_luck"],
                    "rewards": ["cultivation_boost", "rare_item", "technique"]
                },
                {
                    "title": "前辈传承",
                    "description": "遇到一位神秘前辈，愿意传授{character}珍贵功法",
                    "triggers": ["good_karma", "cultivation_bottleneck"],
                    "rewards": ["new_technique", "cultivation_insight"]
                }
            ],
            
            "冲突": [
                {
                    "title": "宗门争端",
                    "description": "{sect1}与{sect2}因为{reason}发生冲突，战火即将燃起",
                    "triggers": ["sect_tension", "resource_competition"],
                    "consequences": ["sect_war", "alliance_shift"]
                },
                {
                    "title": "魔道入侵",
                    "description": "魔道修士在{location}肆虐，正道修士纷纷响应号召",
                    "triggers": ["evil_karma_surge", "celestial_anomaly"],
                    "consequences": ["karma_shift", "power_struggle"]
                }
            ],
            
            "机缘": [
                {
                    "title": "天材地宝",
                    "description": "在{location}发现珍稀的{treasure}，引来众多修士争夺",
                    "triggers": ["celestial_alignment", "spiritual_surge"],
                    "rewards": ["rare_material", "alchemy_ingredient"]
                },
                {
                    "title": "秘境开启",
                    "description": "古老秘境{secret_realm}突然开启，机缘与危险并存",
                    "triggers": ["time_cycle", "collective_cultivation"],
                    "rewards": ["exploration_opportunity", "ancient_knowledge"]
                }
            ]
        }
    
    def initialize_world_state(self):
        """初始化世界状态"""
        # 设置基础宗门关系
        major_sects = ["青云宗", "天音寺", "鬼王宗", "焚香谷", "合欢派"]
        
        for sect1 in major_sects:
            self.world_state.sect_relations[sect1] = {}
            for sect2 in major_sects:
                if sect1 != sect2:
                    # 随机初始关系 (-1到1)
                    relation = random.uniform(-0.5, 0.5)
                    self.world_state.sect_relations[sect1][sect2] = relation
    
    def start_director_cycle(self):
        """开始AI导演循环"""
        # 这里应该启动一个定时任务，简化实现
        self.last_event_time = time.time()
    
    def on_world_event(self, event):
        """处理世界事件"""
        # 分析事件对世界状态的影响
        self.analyze_event_impact(event)
        
        # 考虑生成相关剧情事件
        self.consider_story_generation(event)
    
    def analyze_event_impact(self, event):
        """分析事件对世界的影响"""
        event_type = type(event).__name__
        
        if "Cultivation" in event_type:
            # 修炼事件影响灵气水平
            self.world_state.spiritual_energy_level += 0.001
        
        elif "Combat" in event_type:
            # 战斗事件可能引发冲突
            if hasattr(event, 'character_id'):
                self.update_character_profile(event.character_id, "combat_activity", 1)
        
        elif "Sect" in event_type:
            # 宗门事件影响宗门关系
            if hasattr(event, 'sect_name'):
                self.handle_sect_event(event)
    
    def consider_story_generation(self, trigger_event):
        """考虑生成剧情事件"""
        current_time = time.time()
        
        # 检查冷却时间
        if current_time - self.last_event_time < self.event_cooldown:
            return
        
        # 基于触发事件和世界状态决定是否生成新事件
        generation_chance = self.calculate_generation_chance(trigger_event)
        
        if random.random() < generation_chance:
            story_event = self.generate_story_event(trigger_event)
            if story_event:
                self.activate_story_event(story_event)
                self.last_event_time = current_time
    
    def calculate_generation_chance(self, trigger_event) -> float:
        """计算事件生成概率"""
        base_chance = self.narrative_preferences["frequency"]
        
        # 基于事件类型调整
        event_type = type(trigger_event).__name__
        if "Breakthrough" in event_type:
            base_chance *= 1.5  # 突破事件更容易触发剧情
        elif "Combat" in event_type:
            base_chance *= 1.2
        
        # 基于世界状态调整
        if len(self.active_story_events) > 3:
            base_chance *= 0.5  # 太多活跃事件时降低概率
        
        return min(base_chance, 0.8)
    
    @handler_method
    def generate_story_event(self, trigger_event=None) -> Optional[StoryEvent]:
        """生成剧情事件"""
        # 选择事件类型
        event_type = self.select_event_type(trigger_event)
        
        # 选择事件模板
        templates = self.event_templates.get(event_type.value, [])
        if not templates:
            return None
        
        template = random.choice(templates)
        
        # 生成具体事件
        story_event = self.create_event_from_template(template, event_type)
        
        return story_event
    
    def select_event_type(self, trigger_event) -> StoryEventType:
        """选择事件类型"""
        if trigger_event:
            event_name = type(trigger_event).__name__
            
            if "Breakthrough" in event_name:
                return random.choice([StoryEventType.OPPORTUNITY, StoryEventType.CHALLENGE])
            elif "Combat" in event_name:
                return random.choice([StoryEventType.CONFLICT, StoryEventType.ENCOUNTER])
            elif "Celestial" in event_name:
                return random.choice([StoryEventType.CALAMITY, StoryEventType.OPPORTUNITY])
        
        # 默认随机选择
        return random.choice(list(StoryEventType))
    
    def create_event_from_template(self, template: dict, event_type: StoryEventType) -> StoryEvent:
        """从模板创建事件"""
        # 生成事件ID
        event_id = f"story_{int(time.time())}_{random.randint(1000, 9999)}"
        
        # 填充模板变量
        title = template["title"]
        description = self.fill_template_variables(template["description"])
        
        # 确定参与者
        participants = self.select_participants()
        
        # 确定位置
        location = self.select_location()
        
        # 生成后果
        consequences = self.generate_consequences(template)
        
        return StoryEvent(
            event_id=event_id,
            event_type=event_type,
            title=title,
            description=description,
            participants=participants,
            location=location,
            timestamp=time.time(),
            consequences=consequences,
            narrative_style=self.narrative_preferences["style"]
        )
    
    def fill_template_variables(self, template_text: str) -> str:
        """填充模板变量"""
        variables = {
            "location": self.select_location(),
            "character": self.select_character(),
            "sect1": random.choice(list(self.world_state.sect_relations.keys())),
            "sect2": random.choice(list(self.world_state.sect_relations.keys())),
            "reason": random.choice(["资源争夺", "理念冲突", "历史恩怨", "领土纠纷"]),
            "treasure": random.choice(["千年灵芝", "龙血石", "凤凰羽毛", "星辰铁"]),
            "secret_realm": random.choice(["青云秘境", "古仙洞府", "魔神遗迹", "天书阁"])
        }
        
        result = template_text
        for var, value in variables.items():
            result = result.replace(f"{{{var}}}", str(value))
        
        return result
    
    def select_participants(self) -> List[str]:
        """选择事件参与者"""
        # 简化实现，返回当前角色
        owner = self.get_owner()
        if owner:
            return [str(owner.id)]
        return []
    
    def select_location(self) -> str:
        """选择事件位置"""
        locations = [
            "青云山", "天音寺", "死泽", "焚香谷", "合欢派",
            "蛮荒之地", "东海", "昆仑山", "蜀山", "峨眉"
        ]
        return random.choice(locations)
    
    def select_character(self) -> str:
        """选择角色"""
        owner = self.get_owner()
        if owner:
            return owner.name
        return "修士"
    
    def generate_consequences(self, template: dict) -> Dict[str, Any]:
        """生成事件后果"""
        consequences = {}
        
        if "rewards" in template:
            consequences["potential_rewards"] = template["rewards"]
        
        if "consequences" in template:
            consequences["world_effects"] = template["consequences"]
        
        # 添加随机因素
        consequences["success_chance"] = random.uniform(0.3, 0.9)
        consequences["difficulty"] = random.randint(1, 10)
        
        return consequences
    
    def activate_story_event(self, story_event: StoryEvent):
        """激活剧情事件"""
        self.active_story_events.append(story_event)
        
        # 通知相关角色
        self.notify_participants(story_event)
        
        # 发送世界事件
        if self.event_bus:
            if story_event.event_type == StoryEventType.CALAMITY:
                world_event = CelestialAnomalyEvent(
                    anomaly_type="剧情劫难",
                    location=story_event.location,
                    description=story_event.description
                )
                self.event_bus.emit_event(world_event)
        
        self.log_info(f"激活剧情事件: {story_event.title}")
    
    def notify_participants(self, story_event: StoryEvent):
        """通知参与者"""
        # 这里应该向参与者发送消息
        # 简化实现，只记录日志
        self.log_info(f"剧情事件通知: {story_event.title} - {story_event.description}")
    
    @handler_method
    def get_active_events(self) -> List[dict]:
        """获取活跃事件"""
        events = []
        
        for event in self.active_story_events:
            events.append({
                "event_id": event.event_id,
                "type": event.event_type.value,
                "title": event.title,
                "description": event.description,
                "location": event.location,
                "timestamp": event.timestamp,
                "participants": event.participants,
                "consequences": event.consequences
            })
        
        return events
    
    @handler_method
    def participate_in_event(self, event_id: str, action: str) -> dict:
        """参与剧情事件"""
        # 查找事件
        event = None
        for story_event in self.active_story_events:
            if story_event.event_id == event_id:
                event = story_event
                break
        
        if not event:
            return {"success": False, "message": "事件不存在或已结束"}
        
        # 处理参与行动
        result = self.process_event_action(event, action)
        
        # 如果事件完成，移动到已完成列表
        if result.get("event_completed", False):
            self.active_story_events.remove(event)
            self.completed_events.append(event)
        
        return result
    
    def process_event_action(self, event: StoryEvent, action: str) -> dict:
        """处理事件行动"""
        success_chance = event.consequences.get("success_chance", 0.5)
        
        # 基于行动类型调整成功率
        action_modifiers = {
            "investigate": 0.1,
            "negotiate": 0.05,
            "fight": -0.1,
            "retreat": 0.2
        }
        
        final_chance = success_chance + action_modifiers.get(action, 0)
        success = random.random() < final_chance
        
        if success:
            # 成功处理
            rewards = event.consequences.get("potential_rewards", [])
            selected_reward = random.choice(rewards) if rewards else "经验"
            
            # 应用奖励
            self.apply_event_reward(selected_reward)
            
            return {
                "success": True,
                "message": f"成功{action}，获得{selected_reward}",
                "reward": selected_reward,
                "event_completed": True
            }
        else:
            # 失败处理
            return {
                "success": False,
                "message": f"{action}失败，但获得了宝贵经验",
                "event_completed": random.random() < 0.3  # 30%概率事件结束
            }
    
    def apply_event_reward(self, reward: str):
        """应用事件奖励"""
        owner = self.get_owner()
        if not owner:
            return
        
        if reward == "cultivation_boost" and hasattr(owner, 'cultivation'):
            owner.cultivation.advance_cultivation(500)
        elif reward == "rare_item":
            # 简化实现，添加到背包或记录
            self.log_info(f"获得珍稀物品: {reward}")
        elif reward == "new_technique" and hasattr(owner, 'combat'):
            # 学习新技能的机会
            self.log_info(f"获得新功法学习机会")
    
    @handler_method
    def get_story_status(self) -> dict:
        """获取故事状态"""
        return {
            "active_events": len(self.active_story_events),
            "completed_events": len(self.completed_events),
            "world_era": self.world_state.current_era,
            "spiritual_energy": self.world_state.spiritual_energy_level,
            "celestial_alignment": self.world_state.celestial_alignment,
            "narrative_style": self.narrative_preferences["style"].value,
            "complexity": self.narrative_preferences["complexity"],
            "frequency": self.narrative_preferences["frequency"],
            "player_agency": self.narrative_preferences["player_agency"]
        }

    @handler_method
    def update_context(self, context: str) -> dict:
        """更新剧情上下文"""
        try:
            # 分析上下文并可能触发新事件
            self.analyze_context_for_events(context)

            return {
                "success": True,
                "message": f"上下文已更新: {context}",
                "context": context
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"上下文更新失败: {e}"
            }

    def analyze_context_for_events(self, context: str):
        """分析上下文并可能生成事件"""
        # 简化实现：根据关键词触发不同类型的事件
        if "修炼" in context:
            self.consider_cultivation_event()
        elif "战斗" in context:
            self.consider_combat_event()
        elif "探索" in context:
            self.consider_exploration_event()

    def consider_cultivation_event(self):
        """考虑生成修炼相关事件"""
        # 简化实现
        self.log_info("检测到修炼活动，考虑生成相关剧情事件")

    def consider_combat_event(self):
        """考虑生成战斗相关事件"""
        # 简化实现
        self.log_info("检测到战斗活动，考虑生成相关剧情事件")

    def consider_exploration_event(self):
        """考虑生成探索相关事件"""
        # 简化实现
        self.log_info("检测到探索活动，考虑生成相关剧情事件")

    @handler_method
    def get_world_state(self) -> dict:
        """获取世界状态"""
        return {
            "current_era": self.world_state.current_era,
            "spiritual_energy_level": self.world_state.spiritual_energy_level,
            "celestial_alignment": self.world_state.celestial_alignment,
            "major_events_count": len(self.world_state.major_events),
            "active_conflicts_count": len(self.world_state.active_conflicts),
            "active_story_events": len(self.active_story_events)
        }
    
    @handler_method
    def set_narrative_preferences(self, preferences: dict) -> dict:
        """设置叙事偏好"""
        valid_keys = ["style", "complexity", "frequency", "player_agency"]
        
        for key, value in preferences.items():
            if key in valid_keys:
                if key == "style" and isinstance(value, str):
                    try:
                        self.narrative_preferences[key] = NarrativeStyle(value)
                    except ValueError:
                        continue
                elif key != "style" and isinstance(value, (int, float)):
                    self.narrative_preferences[key] = max(0, min(1, value))
        
        return {
            "success": True,
            "message": "叙事偏好已更新",
            "current_preferences": {
                "style": self.narrative_preferences["style"].value,
                "complexity": self.narrative_preferences["complexity"],
                "frequency": self.narrative_preferences["frequency"],
                "player_agency": self.narrative_preferences["player_agency"]
            }
        }
    
    def update_character_profile(self, character_id: str, attribute: str, value: Any):
        """更新角色档案"""
        if character_id not in self.character_profiles:
            self.character_profiles[character_id] = {}
        
        if attribute not in self.character_profiles[character_id]:
            self.character_profiles[character_id][attribute] = 0
        
        if isinstance(value, (int, float)):
            self.character_profiles[character_id][attribute] += value
        else:
            self.character_profiles[character_id][attribute] = value


# 注册Handler
HandlerRegistry.register(AIDirectorHandler, "ai_director")

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的项目状态检查
"""

import os
import sys

def check_project_structure():
    """检查项目结构"""
    print("🔍 检查项目结构...")
    
    required_dirs = ['server', 'typeclasses', 'world', 'web']
    required_files = ['server/conf/settings.py', 'typeclasses/characters.py']
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✅ 目录存在: {dir_name}")
        else:
            print(f"❌ 目录缺失: {dir_name}")
    
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"✅ 文件存在: {file_name}")
        else:
            print(f"❌ 文件缺失: {file_name}")

def check_configuration():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    try:
        # 检查settings.py
        with open('server/conf/settings.py', 'r', encoding='utf-8') as f:
            settings_content = f.read()
            if 'xiuxian_mud_new' in settings_content:
                print("✅ 项目名称配置正确")
            else:
                print("❌ 项目名称配置异常")
    except Exception as e:
        print(f"❌ 读取settings.py失败: {e}")
    
    try:
        # 检查secret_settings.py
        with open('server/conf/secret_settings.py', 'r', encoding='utf-8') as f:
            secret_content = f.read()
            if 'xiuxian_mud' in secret_content:
                print("✅ 数据库配置存在")
            if 'AI_SETTINGS' in secret_content:
                print("✅ AI配置存在")
    except Exception as e:
        print(f"❌ 读取secret_settings.py失败: {e}")

def check_day3_4_implementation():
    """检查Day3-4实现状态"""
    print("\n🔍 检查Day3-4核心功能实现状态...")
    
    # 检查角色类
    try:
        with open('typeclasses/characters.py', 'r', encoding='utf-8') as f:
            char_content = f.read()
            
        if 'TagProperty' in char_content:
            print("✅ 角色类包含TagProperty")
        else:
            print("❌ 角色类缺少TagProperty实现")
            
        if 'EventHandler' in char_content or 'event' in char_content.lower():
            print("✅ 角色类包含事件相关代码")
        else:
            print("❌ 角色类缺少事件系统集成")
            
        if len(char_content.strip()) < 100:
            print("⚠️  角色类内容较少，可能未完全实现")
            
    except Exception as e:
        print(f"❌ 检查角色类失败: {e}")
    
    # 检查是否有自定义脚本
    scripts_dir = 'typeclasses'
    try:
        files = os.listdir(scripts_dir)
        script_files = [f for f in files if f.endswith('.py') and f != '__init__.py']
        print(f"📊 发现typeclasses文件: {len(script_files)}个")
        for f in script_files:
            print(f"  - {f}")
    except Exception as e:
        print(f"❌ 检查脚本目录失败: {e}")

def check_design_documents():
    """检查设计文档"""
    print("\n🔍 检查设计文档...")
    
    doc_files = [
        '../核心事件总线系统设计.md',
        '../TagProperty高性能查询系统.md', 
        '../Handler生态组件化框架.md'
    ]
    
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            print(f"✅ 设计文档存在: {os.path.basename(doc_file)}")
        else:
            print(f"❌ 设计文档缺失: {os.path.basename(doc_file)}")

def main():
    """主函数"""
    print("🎯 仙侠MUD项目状态检查 (Day3-4)")
    print("=" * 50)
    
    check_project_structure()
    check_configuration()
    check_day3_4_implementation()
    check_design_documents()
    
    print("\n💡 总结:")
    print("- ✅ 基础Evennia项目结构完整")
    print("- ✅ 数据库和AI配置已设置")
    print("- ❌ Day3-4核心功能尚未实现")
    print("- ✅ 详细设计文档已准备")
    
    print("\n📋 下一步建议:")
    print("1. 实现事件驱动总线系统")
    print("2. 实现TagProperty高性能查询系统")
    print("3. 集成AI导演接口")
    print("4. 进行性能基准测试")

if __name__ == "__main__":
    main()

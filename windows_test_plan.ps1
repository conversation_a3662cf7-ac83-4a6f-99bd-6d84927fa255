# 仙侠MUD游戏 Windows环境测试脚本
# 适用于Day3-4阶段的功能验证

Write-Host "🎯 仙侠MUD游戏 Windows环境测试开始" -ForegroundColor Green
Write-Host "=" * 50

# 1. 环境验证测试
Write-Host "📋 1. 基础环境验证" -ForegroundColor Yellow

# 检查虚拟环境
Write-Host "检查Python虚拟环境..."
& "xiuxian_venv\Scripts\python.exe" -c "import sys; print(f'Python版本: {sys.version}')"

# 检查Evennia版本
Write-Host "检查Evennia版本..."
& "xiuxian_venv\Scripts\python.exe" -c "import evennia; print(f'Evennia版本: {evennia.__version__}')"

# 检查数据库连接
Write-Host "检查PostgreSQL连接..."
& "xiuxian_venv\Scripts\python.exe" -c "
import psycopg2
try:
    conn = psycopg2.connect(host='localhost', database='xiuxian_mud', user='postgres', password='zy123good')
    print('✅ PostgreSQL连接成功')
    conn.close()
except Exception as e:
    print(f'❌ PostgreSQL连接失败: {e}')
"

# 检查Redis连接
Write-Host "检查Redis连接..."
& "xiuxian_venv\Scripts\python.exe" -c "
import redis
try:
    r = redis.Redis(host='127.0.0.1', port=6379, db=1)
    r.ping()
    print('✅ Redis连接成功')
except Exception as e:
    print(f'❌ Redis连接失败: {e}')
"

# 2. Evennia服务器状态检查
Write-Host "`n📋 2. Evennia服务器状态检查" -ForegroundColor Yellow

Set-Location "xiuxian_mud_new"
& "..\xiuxian_venv\Scripts\python.exe" -m evennia status

# 3. 基础功能测试
Write-Host "`n📋 3. 基础功能测试" -ForegroundColor Yellow

# 测试基础命令
Write-Host "测试基础Evennia命令..."
& "..\xiuxian_venv\Scripts\python.exe" -c "
import os
os.chdir('.')
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
django.setup()

from evennia import ObjectDB
from evennia.utils import create

# 测试对象创建
try:
    test_obj = create.create_object('typeclasses.objects.Object', key='测试对象')
    print(f'✅ 对象创建成功: {test_obj}')
    test_obj.delete()
    print('✅ 对象删除成功')
except Exception as e:
    print(f'❌ 对象操作失败: {e}')
"

Write-Host "`n🎯 基础环境测试完成" -ForegroundColor Green
Set-Location ".."
